cmake_minimum_required(VERSION 3.8)
project(drill_msgs)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
# uncomment the following section in order to fill in
# further dependencies manually.
# find_package(<dependency> REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(rosidl_default_generators REQUIRED)

rosidl_generate_interfaces(${PROJECT_NAME}
  "msg/Event.msg"
  "msg/Report.msg"
  "msg/Permission.msg"
  "msg/ParamNotification.msg"
  "msg/BoolStamped.msg"
  "msg/Vector2d.msg"
  "msg/IMU.msg"
  "msg/EngineState.msg"
  "msg/DrillStateRaw.msg"
  "msg/DrillState.msg"
  "msg/DepthInfo.msg"
  "msg/FloatStamped.msg"
  "msg/JacksStateRaw.msg"
  "msg/JacksSwitchState.msg"
  "msg/JacksSwitchStateRaw.msg"
  "msg/PinsStateRaw.msg"
  "msg/ArmStateRaw.msg"
  "msg/ForkStateRaw.msg"
  "msg/ForkState.msg"
  "msg/CarouselStateRaw.msg"
  "msg/WrenchStateRaw.msg"
  "msg/DustFlapsState.msg"
  "msg/StateMachineStatus.msg"
  "msg/StateCommand.msg"
  "msg/RmoHealth.msg"
  "msg/CarouselCtrl.msg"
  "msg/TowerCtrl.msg"
  "msg/WrenchCtrl.msg"
  "msg/TracksCtrl.msg"
  "msg/FloatCtrl.msg"
  "msg/DrillCtrl.msg"
  "msg/DrillActuatorCtrl.msg"
  "msg/AirCtrl.msg"
  "msg/JacksCtrl.msg"
  "msg/UpsStatus.msg"
  "msg/LampCtrl.msg"
  "msg/Level.msg"
  "msg/GNSS.msg"
  "msg/Position.msg"
  "msg/SpeedState.msg"
  "msg/TracksState.msg"
  "msg/TowerState.msg"
  "msg/ModeCtrl.msg"
  "msg/Point2d.msg"
  "msg/Path.msg"
  "msg/PathPoint.msg"
  "msg/MainAction.msg"
  "msg/DriveAction.msg"
  "msg/TowerState.msg"
  "msg/DriveStatus.msg"
  "srv/GetCurrentDriveAction.srv"
  DEPENDENCIES std_msgs geometry_msgs # Add packages that above messages depend on
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
