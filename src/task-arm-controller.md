# Задание: Перен<PERSON>с Arm Controller на ROS2

## Обзор

Перенести контроллер люнета (arm_controller) с ROS1 на ROS2 с использованием архитектуры BaseFSM. Люнет - это зажимное устройство, которое фиксирует буровые трубы во время операций.

## Функциональные требования

### Основная функция
Управление открытием и закрытием люнета бурового станка с контролем безопасности и автоматическим восстановлением позиции.

### Входы

1. **Команды управления люнетом**:
   - Топик: `/arm_action`
   - Тип: `drill_msgs/OpenCloseAction`
   - Команды: открыть/закрыть люнет
   - Обработка: через callback (не add_subscriber)

2. **Состояние концевиков люнета**:
   - Топик: `/arm_state`
   - Тип: `drill_msgs/ArmState`
   - Структура:
     ```
     std_msgs/Header header
     bool open
     bool closed
     bool grip
     ```
   - Поля: `open` (bool), `closed` (bool), `grip` (bool)
   - Таймаут: 1.0 сек

3. **Состояние бурового оборудования**:
   - Топик: `/drill_state`
   - Тип: `drill_msgs/DrillState`
   - Используется: `head_pos` (float), `head_pos_is_reliable` (bool)
   - Таймаут: 1.0 сек

4. **Автоматические входы BaseFSM**:
   - '/set_state' (StateCommand)
   - `/permission` (Permission)
   - `/robomode` (BoolStamped)
   - `/main_state_machine_status` (StateMachineStatus)

### Выходы

1. **Команды управления люнетом**:
   - Топик: `/arm_ctrl`
   - Тип: `drill_msgs/FloatCtrl`
   - Значения: отрицательные (открытие), положительные (закрытие), 0 (стоп)

2. **Статус контроллера**:
   - Топик: `/arm_controller_status`
   - Тип: `drill_msgs/StateMachineStatus`
   - Поля: `cur_action_id` (int32), `last_action_id` (int32), `state` (string)

3. **Автоматические выходы BaseFSM**:
   - `/events` (Event)
   - `/internal_report` (Report)

### Управление

**Основное управление через OpenCloseAction**:
```python
# Команда открытия люнета
action_msg = OpenCloseAction()
action_msg.id = 123  # уникальный ID задания
action_msg.action = OpenCloseAction.OPEN

# Команда закрытия люнета
action_msg = OpenCloseAction()
action_msg.id = 124  # уникальный ID задания
action_msg.action = OpenCloseAction.CLOSE
```

**Проверка завершения задания**:
```python
# Задание завершено, если:
action_completed = (status.cur_action_id == -1 and
                    status.last_action_id == expected_action_id)
```

**Дополнительное управление через StateCommand** (для отладки):
```python
# Прямое управление состояниями
state_cmd = StateCommand()
state_cmd.node_name = "arm_controller"
state_cmd.state = "opening"  # или "closing", "open", "prev"
```

## Архитектура FSM

### Состояния

1. **open** (начальное):
   - Остановка управления (ctrl=0)
   - Контроль фактического состояния концевиков
   - Автовосстановление при потере позиции
   - Обработка команд закрытия
   - Сброс флага активного задания при входе в состояние

2. **opening**:
   - Подача команды открытия (ctrl = open_ctrl = -1.0)
   - Контроль таймаута операции
   - Обнаружение застревания
   - Переход в open при срабатывании концевика с удержанием команды

3. **closing**:
   - Проверка безопасности по позиции бурголовки
   - Подача команды закрытия (ctrl = close_ctrl = 1.0)
   - Контроль таймаутов и обнаружение застревания
   - Сложная логика перехода в closed с учетом датчика зажима

4. **closed**:
   - Остановка управления (ctrl=0)
   - Защита по глубине с автоматическим открытием
   - Автовосстановление при потере фиксации
   - Обработка команд открытия
   - Сброс флага активного задания при входе в состояние

### Диаграмма переходов

```
  open ←→ opening
   ↑        ↓
   ↓        ↑
 closed ←→ closing
```

## Логика безопасности

### Проверки безопасности

1. **Позиция бурголовки**: Запрет закрытия при `drill_state.head_pos > max_head_pos_to_close`. Если уже в `closed` и порог превышен — немедленно перейти в `opening`.
2. **Валидность данных**: Проверка `drill_state.head_pos_is_reliable` и `drill_state.is_valid`
3. **Консистентность концевиков**: Недопустимо одновременное срабатывание `open` и `closed`
4. **Таймауты операций**: Контроль времени выполнения команд только в активном состоянии
5. **Флаг arm_present**: При `arm_present=false` автоматический переход в состояние `open`
6. **Режимы работы**: Работа только в разрешенных режимах

### События безопасности

- `ACTION_NOT_ACCEPTED` - команда не может быть выполнена
- `ACTION_COMPLETED` - задание успешно завершено
- `ARM_OPEN_FAILED` - не удалось открыть люнет (таймаут)
- `ARM_CLOSE_FAILED` - не удалось закрыть люнет (таймаут)
- `ARM_HEAD_TOO_LOW` - бурголовка слишком низко для закрытия
- `ARM_SWITCH_INCONSISTENT` - неисправность концевых выключателей
- `ARM_STUCK_OPENING` - люнет застрял при открытии
- `ARM_STUCK_CLOSING` - люнет застрял при закрытии

События определены в `base_node/base_node/event_codes.py`.

## Параметры конфигурации

Добавить в `params_server/params_server/base_config/nodes.yaml`:

```yaml
ArmController:
  rate: 10.0                    # Частота работы FSM (Гц)
  opening_time: 30.0            # Таймаут открытия (сек)
  closing_time: 30.0            # Таймаут закрытия (сек)
  open_push_time: 0.5           # Время удержания в открытом состоянии (сек)
  close_push_time: 0.5          # Время удержания в закрытом состоянии (сек)
  grip_push_time: 1.5           # Доп. время на «дожим» захвата (сек), если нет датчика
  no_reaction_time: 5.5         # Таймаут отсутствия реакции (сек)
  max_head_pos_to_close: 13.0   # Максимальная позиция бурголовки для закрытия (м)
  arm_present: true             # Флаг наличия люнета в системе
  arm_grip_sensor_present: true # Флаг наличия датчика зажима
  open_ctrl: -1.0               # Команда управления для открытия
  close_ctrl: 1.0               # Команда управления для закрытия
  allowed_modes:                # Разрешенные режимы работы
    - "drilling"
    - "tower_tilt"
    - "shaft_buildup"
    - "shaft_stow"
    - "idle"
    - "moving"
  switch_inconsistent_time: 0.3 # Время для фиксации неконсистентности концевиков
```

Параметры должны поддерживать динамическое обновление через механизм параметров `BaseNode` (наш самописный сервер параметров). Все изменения значений, приходящие с сервера, должны применяться без перезапуска ноды.

## Сообщения

- `drill_msgs/OpenCloseAction` — использовать поля `id:int32` и `action:int32` (значения: `OPEN=1`, `CLOSE=-1`).
- `drill_msgs/ArmState` — требуется добавить в пакет сообщений, если его ещё нет, со структурой:

```text
std_msgs/Header header
bool open
bool closed
bool grip
```

## Структура реализации

### Структура пакета

```
src/controllers/arm_controller/
├── arm_controller/
│   ├── __init__.py
│   ├── arm_controller.py      # Основной FSM класс
│   └── states.py              # Классы состояний
├── launch/
│   └── arm_controller.launch.xml
├── README.md
├── package.xml
└── setup.py
```

### Основной класс FSM

```python
from base_node.base_fsm import BaseFSM
from drill_msgs.msg import ArmState, DrillState, FloatCtrl, OpenCloseAction
from .states import OpeningState, OpenState, ClosingState, ClosedState

class ArmControllerFSM(BaseFSM):
    def __init__(self):
        super().__init__(node_name="arm_controller")

        # Регистрация состояний
        self.add_states(
            OpenState(self),
            OpeningState(self),
            ClosingState(self),
            ClosedState(self)
        )

        # Подписчики с таймаутами
        self.add_subscriber("/arm_state", ArmState, "arm_state", timeout=1.0)
        self.add_subscriber("/drill_state", DrillState, "drill_state", timeout=1.0)

        # Подписка на команды управления
        self.create_subscription(OpenCloseAction, "/arm_action", self.action_callback, 10)

        # Публикатор команд
        self.control_pub = self.create_publisher(FloatCtrl, "/arm_ctrl", 10)
        self._arm_ctrl = 0.0

        # Переменные для отслеживания заданий
        self.new_action_flag = False
        self.do_open = False
        self._cur_action_id = -1
        self._last_action_id = -1

        # Установка начального состояния
        self.set_state("open")

    def stop_control(self):
        """Безопасная остановка люнета"""
        msg = FloatCtrl()
        msg.header.stamp = self.get_clock().now().to_msg()
        msg.ctrl = 0.0
        self.control_pub.publish(msg)

    def safety_check(self) -> bool:
        """Проверки безопасности"""
        # Проверка arm_present флага
        if not self.params.arm_present:
            if self.get_current_state_name() != "open":
                self.set_state("open")
            return False

        # Проверка валидности данных бурения
        if not self.subs.drill_state.is_valid or not self.subs.drill_state.data.head_pos_is_reliable:
            return False

        # Проверка консистентности концевиков
        if self.subs.arm_state.is_valid:
            if self.subs.arm_state.data.open and self.subs.arm_state.data.closed:
                self.handle_error(
                    "Inconsistent switch state!",
                    event_code="ARM_SWITCH_INCONSISTENT"
                )
                return False

        return True

    def do_work_finally(self):
        """Единая точка публикации управления"""
        msg = FloatCtrl()
        msg.header.stamp = self.get_clock().now().to_msg()
        msg.ctrl = self._arm_ctrl
        self.control_pub.publish(msg)

    def action_callback(self, msg: OpenCloseAction):
        """Обработка команд управления люнетом"""
        current_state = self.get_current_state_name()

        # Проверка валидности команды
        if msg.action not in [OpenCloseAction.OPEN, OpenCloseAction.CLOSE]:
            self.handle_error("Action data is incomplete or invalid",
                            event_code="ACTION_NOT_ACCEPTED")
            return

        # Проверка необходимости действия
        if (msg.action == OpenCloseAction.OPEN and current_state == "open") or \
           (msg.action == OpenCloseAction.CLOSE and current_state == "closed"):
            self.log("Already in requested state, do nothing!")
            return

        # Обработка команд
        if msg.action == OpenCloseAction.OPEN and current_state in ["closed", "closing"]:
            self.new_action_flag = True
            self.do_open = True
            self._cur_action_id = msg.id
            if current_state == "closed":
                self.set_state("opening")
        elif msg.action == OpenCloseAction.CLOSE and current_state in ["open", "opening"]:
            self.new_action_flag = True
            self.do_open = False
            self._cur_action_id = msg.id
            if current_state == "open":
                self.set_state("closing")
        else:
            self.handle_error(
                f"Invalid command or state. action={msg.action}, current_state={current_state}",
                event_code="ACTION_NOT_ACCEPTED"
            )
```

### Классы состояний

Базовая структура состояний с использованием параметров и проверок безопасности. Каждое состояние должно реализовать методы `on_transition_to()` и `do_work()` согласно архитектуре BaseFSM.

## Launch файл

```xml
<?xml version="1.0"?>
<launch>
    <!-- Переменные окружения для BaseNode -->
    <set_env name="PARAM_SERVER_ADDRESS" value="localhost" />
    <set_env name="PARAM_SERVER_PORT" value="5000" />
    <set_env name="REPORT_RATE" value="5.0" />
    
    <!-- Запуск контроллера люнета -->
    <node pkg="arm_controller"
          exec="arm_controller"
          name="arm_controller"
          output="screen"
          respawn="true"
          respawn_delay="5" />
</launch>
```

## Критерии приемки

1. **Функциональность**: Корректное открытие/закрытие люнета по командам
2. **Безопасность**: Запрет закрытия при глубоком бурении
3. **Надежность**: Автовосстановление при потере позиции
4. **Диагностика**: Корректная публикация событий и отчетов
5. **Интеграция**: Работа с системой параметров и BaseFSM
6. **Документация**: Полная документация README.md с входами, выходами, параметрами и логикой работы

## Детализация логики состояний

Все временные проверки должны использовать `get_current_state_duration()` из BaseFSM, который возвращает "чистое" время нахождения в состоянии, исключая периоды блокировки.

### Состояние opening

- Публиковать `FloatCtrl(ctrl=open_ctrl)` (-1.0)
- Таймаут операции: если `get_current_state_duration() > opening_time` — событие `ARM_OPEN_FAILED`
- Обнаружение застревания: если спустя `no_reaction_time` остаётся признак застревания (при наличии датчика `grip==True`, иначе `closed==True`) — событие `ARM_STUCK_OPENING`
- Логика push_time: сбрасывать таймер каждый раз когда `open==False`, переходить в `open` только когда `open==True` непрерывно в течение `open_push_time`

### Состояние open

- Остановка управления (`ctrl=0`)
- Сброс флага активного задания: `_cur_action_id = -1`, `_last_action_id = предыдущий_id` при входе в состояние
- Если `open==False` — восстановление через переход в `opening` с логированием "Restore open"
- Если пришла команда закрытия (`new_action_flag && !do_open`) — переход в `closing`

### Состояние closing

- Проверка безопасности по глубине: если `drill_state.head_pos > max_head_pos_to_close` — событие `ARM_HEAD_TOO_LOW`, остановка (`ctrl=0`) и переход в `opening`
- Публиковать `FloatCtrl(ctrl=close_ctrl)` (1.0)
- Таймаут операции: если `get_current_state_duration() > closing_time` — событие `ARM_CLOSE_FAILED`
- Обнаружение застревания: если по истечении `no_reaction_time` `open==True` — событие `ARM_STUCK_CLOSING`
- Логика перехода в closed:
  - Если `arm_grip_sensor_present==True`: сбрасывать таймер когда `!closed || !grip`, переходить в `closed` когда `closed && grip` непрерывно в течение `close_push_time`
  - Иначе: сбрасывать таймер когда `!closed`, переходить в `closed` когда `closed` непрерывно в течение `grip_push_time + close_push_time`

### Состояние closed

- Остановка управления (`ctrl=0`)
- Сброс флага активного задания: `_cur_action_id = -1`, `_last_action_id = предыдущий_id` при входе в состояние
- Защита по глубине: если `drill_state.head_pos > max_head_pos_to_close` — событие `ARM_HEAD_TOO_LOW` и переход в `opening`
- Восстановление фиксации: если `!closed` или (при наличии датчика `!grip`) — логирование "Restore closed" и переход в `closing`
- Если пришла команда открытия (`new_action_flag && do_open`) — переход в `opening`

### Логика завершения заданий

Для унификации с другими контроллерами системы используется единая логика проверки завершения заданий:

```python
# Задание завершено, если:
action_completed = (cur_action_id == -1 and
                    last_action_id == expected_action_id)
```

**Жизненный цикл задания:**
1. **Получение задания**: `cur_action_id = action_id`, `last_action_id` не меняется
2. **Выполнение**: `cur_action_id = action_id`, `last_action_id` не меняется
3. **Завершение**: `cur_action_id = -1`, `last_action_id = action_id` (при переходе в целевое состояние)

## Следующие шаги

После успешной реализации arm_controller использовать его как шаблон для переноса остальных контроллеров (tower, wrench, carousel) с аналогичной архитектурой.
