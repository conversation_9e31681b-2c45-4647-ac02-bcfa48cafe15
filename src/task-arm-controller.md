# Задание: Перенос Arm Controller на ROS2

## Обзор

Перенести контроллер люнета (arm_controller) с ROS1 на ROS2 с использованием архитектуры BaseFSM. Люнет - это зажимное устройство, которое фиксирует буровые трубы во время операций.

## Функциональные требования

### Основная функция
Управление открытием и закрытием люнета бурового станка с контролем безопасности и автоматическим восстановлением позиции.

### Входы

Подписываться с помощью add_subscriber из BaseFSM (base_fsm.py)

1. **Команды управления люнетом**:
   - Топик: `/arm_action`
   - Тип: `drill_msgs/OpenCloseAction`
   - Команды: открыть/закрыть люнет
   - Обработка: через callback (не add_subscriber)

2. **Состояние концевиков люнета**:
   - Топик: `/arm_state`
   - Тип: `drill_msgs/ArmState`
   - Структура:
     ```
     std_msgs/Header header
     bool open
     bool closed
     bool grip
     ```
   - Поля: `open` (bool), `closed` (bool), `grip` (bool)
   - Таймаут: 1.0 сек

3. **Состояние бурового оборудования**:
   - Топик: `/drill_state`
   - Тип: `drill_msgs/DrillState`
   - Используется: `head_pos` (float), `head_pos_is_reliable` (bool)
   - Таймаут: 1.0 сек

4. **Автоматические входы BaseFSM**:
   - '/set_state' (StateCommand)
   - `/permission` (Permission)
   - `/robomode` (BoolStamped)
   - `/main_state_machine_status` (StateMachineStatus) - подписаться, если нет в автоматических 

### Выходы

1. **Команды управления люнетом**:
   - Топик: `/arm_ctrl`
   - Тип: `drill_msgs/FloatCtrl`
   - Значения: положительные (открытие), отрицательные (закрытие), 0 (стоп)

2. **Автоматические выходы BaseFSM**:
   - `/events` (Event)
   - `/internal_report` (Report)
   - `/arm_controller_status` (StateMachineStatus)

### Управление

**Основное управление через OpenCloseAction**:
```python
# Команда открытия люнета
action_msg = OpenCloseAction()
action_msg.id = 0  # опционально
action_msg.action = OpenCloseAction.OPEN

# Команда закрытия люнета
action_msg = OpenCloseAction()
action_msg.id = 0  # опционально
action_msg.action = OpenCloseAction.CLOSE
```

**Дополнительное управление через StateCommand** (для отладки):
```python
# Прямое управление состояниями
state_cmd = StateCommand()
state_cmd.node_name = "arm_controller"
state_cmd.state = "opening"  # или "closing", "idle", "prev"
```

## Архитектура FSM

### Состояния

**ВАЖНО:** ROS1 реализация не имеет состояния `idle` и начинает работу в состоянии `open`. Для совместимости рекомендуется:
- Либо убрать состояние `idle` и начинать с `open` (как в ROS1)
- Либо сделать `idle` промежуточным состоянием только для инициализации

1. **idle** (опциональное начальное):
   - Остановка управления
   - Ожидание команд
   - Проверка соответствия фактического состояния
   - **НОВОЕ:** Проверка arm_present флага - если false, переход в open
   - **НОВОЕ:** Блокировка при REMOTE режимах

2. **opening**:
   - Подача команды открытия (значение по параметру `open_ctrl`, по умолчанию положительное)
   - Контроль таймаута
   - Переход в open при срабатывании концевика

3. **open**:
   - Удержание в открытом состоянии
   - Контроль фактического состояния
   - Автовосстановление при потере позиции

4. **closing**:
   - Проверка безопасности (позиция бурголовки)
   - Подача команды закрытия (значение по параметру `close_ctrl`, по умолчанию отрицательное)
   - Контроль таймаутов и реакции

5. **closed**:
   - Проверка безопасности по позиции бурголовки
   - Автоматическое открытие при превышении
   - Контроль фактического состояния

### Диаграмма переходов

```
    idle
   ↙    ↘
opening  closing
   ↓      ↓
  open   closed
   ↑      ↑
   └──────┘
```

## Логика безопасности

### Проверки безопасности

1. **Позиция бурголовки**: Запрет закрытия при `drill_state.head_pos > max_head_pos_to_close`. Если уже в `closed` и порог превышен — немедленно перейти в `opening` (самозащита).
2. **Валидность данных**: Проверка `drill_state.head_pos_is_reliable`
3. **Консистентность концевиков**: Недопустимо одновременное срабатывание `open` и `closed`
4. **Таймауты операций**: Контроль времени выполнения команд

### События безопасности

- `rc_open_arm` - не удалось открыть люнет
- `rc_close_arm` - не удалось закрыть люнет
- `head_too_low` - бурголовка слишком низко для закрытия
- `switch_failure` - неисправность концевых выключателей
- **НОВОЕ:** `already_done` - команда уже выполнена (из ROS1)

### Дополнительные проверки безопасности (из ROS1)

1. **Режимы работы**: Блокировка при REMOTE режимах (REMOTE, REMOTE_WAIT, REMOTE_PREPARE)
2. **Валидность данных**: Проверка `drill_state.is_valid` перед выполнением операций
3. **Флаг arm_present**: При `arm_present=false` автоматический переход в состояние `open`
4. **Обнаружение застревания**: Различная логика для систем с датчиком зажима и без него

Требуется добавить отсутствующие коды событий в `base_node/base_node/event_codes.py` (или использовать существующие подходящие коды), чтобы их можно было указывать при логировании через `BaseNode.log/handle_error`.

## Параметры конфигурации

Добавить в `params_server/params_server/base_config/nodes.yaml`:

```yaml
ArmController:
  rate: 10.0                    # Частота работы FSM (Гц)
  # ИСПРАВЛЕНО: Используем проверенные значения из ROS1 реализации
  opening_time: 30.0            # Таймаут открытия (сек) - из ROS1: 30
  closing_time: 30.0            # Таймаут закрытия (сек) - из ROS1: 30
  open_push_time: 0.5           # Время удержания в открытом состоянии (сек) - из ROS1: 0.5
  close_push_time: 0.5          # Время удержания в закрытом состоянии (сек) - из ROS1: 0.5
  grip_push_time: 1.5           # Доп. время на «дожим» захвата (сек), если нет датчика - из ROS1: 1.5
  no_reaction_time: 5.5         # Таймаут отсутствия реакции (сек) - из ROS1: 5.5
  max_head_pos_to_close: 13.0   # Максимальная позиция бурголовки для закрытия (м) - из ROS1: 13.0
  arm_present: true             # Флаг наличия люнета в системе
  arm_grip_sensor_present: true # Флаг наличия датчика зажима
  # ИСПРАВЛЕНО: Направления управления из ROS1 реализации
  open_ctrl: -1.0               # Команда управления для открытия (ROS1: arm_ctrl = -1)
  close_ctrl: 1.0               # Команда управления для закрытия (ROS1: arm_ctrl = 1)
  allowed_modes:                # Разрешенные режимы работы - расширено из ROS1
    - "drilling"
    - "tower_tilt"
    - "shaft_buildup"
    - "shaft_stow"
    - "idle"
    - "moving"
  # ДОБАВЛЕНО: Блокировка REMOTE режимов (из ROS1 логики)
  blocked_modes:                # Режимы, блокирующие работу контроллера
    - "REMOTE"
    - "REMOTE_WAIT"
    - "REMOTE_PREPARE"
  switch_inconsistent_time: 0.3 # (опц.) время для фиксации неконсистентности концевиков (оба активны)
```

Параметры должны поддерживать динамическое обновление через механизм параметров `BaseNode` (наш самописный сервер параметров). Все изменения значений, приходящие с сервера, должны применяться без перезапуска ноды.

## Сообщения

- `drill_msgs/OpenCloseAction` — использовать поля `id:int32` и `action:int32` (значения: `OPEN=1`, `CLOSE=-1`).
- `drill_msgs/ArmState` — требуется добавить в пакет сообщений, если его ещё нет, со структурой:

```text
std_msgs/Header header
bool open
bool closed
bool grip
```

## Структура реализации

### Структура пакета

```
src/controllers/arm_controller/
├── arm_controller/
│   ├── __init__.py
│   ├── arm_controller.py      # Основной FSM класс
│   └── states.py              # Классы состояний
├── launch/
│   └── arm_controller.launch.xml
├── README.md
├── package.xml
└── setup.py
```

### Основной класс FSM

```python
from base_node.base_fsm import BaseFSM
from drill_msgs.msg import ArmState, DrillState, FloatCtrl, OpenCloseAction
from .states import IdleState, OpeningState, OpenState, ClosingState, ClosedState

class ArmControllerFSM(BaseFSM):
    def __init__(self):
        super().__init__(node_name="arm_controller")
        
        # Регистрация состояний
        self.add_states(
            IdleState(self),
            OpeningState(self),
            OpenState(self),
            ClosingState(self),
            ClosedState(self)
        )
        
        # Подписчики с таймаутами
        self.add_subscriber("/arm_state", ArmState, "arm_state", timeout=1.0)
        self.add_subscriber("/drill_state", DrillState, "drill_state", timeout=1.0)

        # ДОБАВЛЕНО: Подписка на режим работы для блокировки REMOTE режимов (если нужно)
        # self.add_subscriber("/main_mode", VehicleBehaviorMode, "main_mode", timeout=1.0)

        # Подписка на команды управления
        self.create_subscription(OpenCloseAction, "/arm_action", self.action_callback, 10)

        # Публикатор команд (централизованная публикация)
        self.control_pub = self.create_publisher(FloatCtrl, "/arm_ctrl", 10)
        self._arm_ctrl = 0.0
        
        # ИСПРАВЛЕНО: Начальное состояние как в ROS1 - open, а не idle
        self.set_state("open")  # ROS1 начинает с OpenState
    
    def stop_control(self):
        """Безопасная остановка люнета"""
        msg = FloatCtrl()
        msg.header.stamp = self.get_clock().now().to_msg()  # ROS2 правильный способ
        msg.ctrl = 0.0
        self.control_pub.publish(msg)
    
    def safety_check(self) -> bool:
        """Проверки безопасности (расширено из ROS1 логики)"""
        # ДОБАВЛЕНО: Проверка arm_present флага
        if not self.params.arm_present:
            if self.get_current_state_name() != "open":
                self.set_state("open")
            return False

        # ДОБАВЛЕНО: Проверка валидности данных бурения (из ROS1)
        if not self.subs.drill_state.is_valid or not self.subs.drill_state.data.head_pos_is_reliable:
            return False

        # ДОБАВЛЕНО: Блокировка REMOTE режимов (из ROS1)
        # Примечание: main_mode может быть добавлен через add_subscriber если нужен
        # if hasattr(self.subs, 'main_mode') and self.subs.main_mode is not None:
        #     blocked_modes = ["REMOTE", "REMOTE_WAIT", "REMOTE_PREPARE"]
        #     if hasattr(self.subs.main_mode.data, 'mode') and self.subs.main_mode.data.mode in blocked_modes:
        #         return False

        # Проверка консистентности концевиков
        if self.subs.arm_state.is_valid:
            if self.subs.arm_state.data.open and self.subs.arm_state.data.closed:
                self.handle_error(
                    "Inconsistent switch state!",
                    event_code="switch_failure"  # Используем строковый код события
                )
                return False

        return True
    
    def do_work_finally(self):
        """Единая точка публикации управления (как в ROS1 publish_arm_ctrl)."""
        msg = FloatCtrl()
        msg.header.stamp = self.get_clock().now().to_msg()  # ROS2 правильный способ
        msg.ctrl = self._arm_ctrl
        self.control_pub.publish(msg)

    def action_callback(self, msg: OpenCloseAction):
        """Обработка команд управления люнетом (логика из ROS1)"""
        current_state = self.get_current_state_name()

        # ДОБАВЛЕНО: Проверка валидности команды (из ROS1)
        if msg.action not in [OpenCloseAction.OPEN, OpenCloseAction.CLOSE]:
            self.handle_error("Action data is incomplete or invalid")
            return

        # ДОБАВЛЕНО: Логика из ROS1 - проверка необходимости действия
        if (msg.action == OpenCloseAction.OPEN and current_state == "open") or \
           (msg.action == OpenCloseAction.CLOSE and current_state == "closed"):
            self.log(
                "Already in requested state, do nothing!",
                event=self.events.ALREADY_DONE
            )
            return

        # ИСПРАВЛЕНО: Убрано idle из допустимых состояний (ROS1 не имеет idle)
        if msg.action == OpenCloseAction.OPEN and current_state in ["closed", "closing"]:
            self.new_action_flag = True
            self.do_open = True
            self._cur_action_seq = msg.id
            self._last_action_seq = msg.id
            if current_state == "closed":  # Переход только если уже в closed
                self.set_state("opening")
        elif msg.action == OpenCloseAction.CLOSE and current_state in ["open", "opening"]:
            self.new_action_flag = True
            self.do_open = False
            self._cur_action_seq = msg.id
            self._last_action_seq = msg.id
            if current_state == "open":  # Переход только если уже в open
                self.set_state("closing")
        else:
            self.log(
                f"Invalid command or state. action={msg.action}, current_state={current_state}"
            )
```

### Классы состояний

**ВАЖНЫЕ ИЗМЕНЕНИЯ:** Добавлены недостающие переменные состояния и логика из ROS1:

```python
# Дополнительные переменные класса для совместимости с ROS1
class ArmControllerFSM(BaseFSM):
    def __init__(self):
        # ... существующий код ...

        # ДОБАВЛЕНО: Переменные из ROS1 реализации
        self.new_action_flag = False
        self.do_open = False
        self._cur_action_seq = -1
        self._last_action_seq = -1

        # ДОБАВЛЕНО: Временные метки для push логики
        self.time_open_push_started = 0.0
        self.time_close_push_started = 0.0

    def get_change_state_timestamp(self):
        """Получить время последнего изменения состояния (аналог ROS1)"""
        return self.get_current_state_start_time()

    @property
    def last_empty_loop_ts(self):
        """Время последнего пустого цикла (для совместимости с ROS1 логикой)"""
        # В BaseFSM это может быть реализовано через механизм таймаутов
        return self.get_clock().now().nanoseconds / 1e9  # ROS2 правильный способ
```

Базовая структура состояний с использованием параметров и проверок безопасности.

## Launch файл

```xml
<?xml version="1.0"?>
<launch>
    <!-- Переменные окружения для BaseNode -->
    <set_env name="PARAM_SERVER_ADDRESS" value="localhost" />
    <set_env name="PARAM_SERVER_PORT" value="5000" />
    <set_env name="REPORT_RATE" value="5.0" />
    
    <!-- Запуск контроллера люнета -->
    <node pkg="arm_controller"
          exec="arm_controller"
          name="arm_controller"
          output="screen"
          respawn="true"
          respawn_delay="5" />
</launch>
```

## Критерии приемки

1. **Функциональность**: Корректное открытие/закрытие люнета по командам
2. **Безопасность**: Запрет закрытия при глубоком бурении
3. **Надежность**: Автовосстановление при потере позиции
4. **Диагностика**: Корректная публикация событий и отчетов
5. **Интеграция**: Работа с системой параметров и BaseFSM
6. **Документация**: Полная документация README.md с входами, выходами, параметрами и логикой работы

## Детализация логики состояний (с учётом ROS1-реализации)

**КРИТИЧЕСКИ ВАЖНО:** Все временные проверки должны использовать `max(get_change_state_timestamp(), last_empty_loop_ts)` как в ROS1, а не простое время состояния.

- **opening**:
  - Публиковать `FloatCtrl(ctrl=open_ctrl)` (ИСПРАВЛЕНО: -1.0 из ROS1).
  - **ДОБАВЛЕНО:** Таймаут общего времени: если `current_time - max(state_start, last_empty_loop) > opening_time` — событие `rc_open_arm` и критическая ошибка.
  - **ДОБАВЛЕНО:** Обнаружение застревания: если спустя `no_reaction_time` остаётся признак «застревания» (при наличии датчика `grip`: `grip==True`, иначе — `closed==True`) — событие `rc_open_arm` и критическая ошибка.
  - **УТОЧНЕНО:** Логика push_time: сбрасывать `time_open_push_started` каждый раз когда `open==False`, переходить в `open` только когда `open==True` непрерывно в течение `open_push_time`.

- **open**:
  - Останов управления (`ctrl=0`).
  - **ДОБАВЛЕНО:** Сброс флага действия: `_cur_action_seq = -1` при входе в состояние.
  - Если `open==False` — «восстановление» через переход в `opening` с логированием "Restore open".
  - Если пришла команда закрытия (`new_action_flag && !do_open`) — переход в `closing`.

- **closing**:
  - **ДОБАВЛЕНО:** Проверка безопасности по глубине: если `drill_state.head_pos > max_head_pos_to_close` — событие `head_too_low`, останов (`ctrl=0`) и переход в `opening`.
  - Публиковать `FloatCtrl(ctrl=close_ctrl)` (ИСПРАВЛЕНО: 1.0 из ROS1).
  - **ДОБАВЛЕНО:** Таймаут общего времени: если `current_time - max(state_start, last_empty_loop) > closing_time` — событие `rc_close_arm` и критическая ошибка.
  - **ДОБАВЛЕНО:** Обнаружение застревания: если по истечении `no_reaction_time` `open==True` — событие `rc_close_arm` и критическая ошибка.
  - **УТОЧНЕНО:** Логика перехода в closed:
    - Если `arm_grip_sensor_present==True`: сбрасывать `time_close_push_started` когда `!closed || !grip`, переходить в `closed` когда `closed && grip` непрерывно в течение `close_push_time`.
    - Иначе: сбрасывать `time_close_push_started` когда `!closed`, переходить в `closed` когда `closed` непрерывно в течение `grip_push_time + close_push_time`.

- **closed**:
  - Останов управления (`ctrl=0`).
  - **ДОБАВЛЕНО:** Сброс флага действия: `_cur_action_seq = -1` при входе в состояние.
  - **ДОБАВЛЕНО:** Защита по глубине: если `drill_state.head_pos > max_head_pos_to_close` — событие `head_too_low` и переход в `opening`.
  - **УТОЧНЕНО:** Восстановление фиксации: если `!closed` или (при наличии датчика `!grip`) — логирование "Restore closed" и переход в `closing`.
  - Если пришла команда открытия (`new_action_flag && do_open`) — переход в `opening`.

**ВАЖНО:** Все проверки безопасности должны выполняться в `safety_check()` и через встроенные проверки `BaseFSM` (permission, robomode, таймауты подписчиков). Для временных проверок использовать правильные временные метки как в ROS1.

## Следующие шаги

После успешной реализации arm_controller использовать его как шаблон для переноса остальных контроллеров (tower, wrench, carousel) с аналогичной архитектурой.
