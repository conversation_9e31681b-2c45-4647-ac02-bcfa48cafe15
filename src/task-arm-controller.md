# Задание: Перенос Arm Controller на ROS2

## Обзор

Перенести контроллер люнета (arm_controller) с ROS1 на ROS2 с использованием архитектуры BaseFSM. Люнет - это зажимное устройство, которое фиксирует буровые трубы во время операций.

## Функциональные требования

### Основная функция
Управление открытием и закрытием люнета бурового станка с контролем безопасности и автоматическим восстановлением позиции.

### Входы

Подписываться с помощью add_subscriber из BaseFSM (base_fsm.py)

1. **Команды управления люнетом**:
   - Топик: `/arm_action`
   - Тип: `drill_msgs/OpenCloseAction`
   - Команды: открыть/закрыть люнет
   - Обработка: через callback (не add_subscriber)

2. **Состояние концевиков люнета**:
   - Топик: `/arm_state`
   - Тип: `drill_msgs/ArmState
            Header header
            bool open
            bool closed
            bool grip`
   - Поля: `open` (bool), `closed` (bool), `grip` (bool)
   - Таймаут: 1.0 сек

3. **Состояние бурового оборудования**:
   - Топик: `/drill_state`
   - Тип: `drill_msgs/msg/DrillState`
   - Используется: `head_pos` (float), `head_pos_is_reliable` (bool)
   - Таймаут: 1.0 сек

4. **Автоматические входы BaseFSM**:
   - '/set_state' (StateCommand)
   - `/permission` (Permission)
   - `/robomode` (BoolStamped)
   - `/main_state_machine_status` (StateMachineStatus) - подписаться, если нет в автоматических 

### Выходы

1. **Команды управления люнетом**:
   - Топик: `/arm_ctrl`
   - Тип: `drill_msgs/FloatCtrl`
   - Значения: положительные (открытие), отрицательные (закрытие), 0 (стоп)

2. **Автоматические выходы BaseFSM**:
   - `/events` (Event)
   - `/internal_report` (Report)
   - `/arm_controller_status` (StateMachineStatus)

### Управление

**Основное управление через OpenCloseAction**:
```python
# Команда открытия люнета
action_msg = OpenCloseAction()
action_msg.id = 0  # опционально
action_msg.action = OpenCloseAction.OPEN

# Команда закрытия люнета
action_msg = OpenCloseAction()
action_msg.id = 0  # опционально
action_msg.action = OpenCloseAction.CLOSE
```

**Дополнительное управление через StateCommand** (для отладки):
```python
# Прямое управление состояниями
state_cmd = StateCommand()
state_cmd.node_name = "arm_controller"
state_cmd.state = "opening"  # или "closing", "idle", "prev"
```

## Архитектура FSM

### Состояния

1. **idle** (начальное):
   - Остановка управления
   - Ожидание команд
   - Проверка соответствия фактического состояния

2. **opening**:
   - Подача команды открытия (значение по параметру `open_ctrl`, по умолчанию положительное)
   - Контроль таймаута
   - Переход в open при срабатывании концевика

3. **open**:
   - Удержание в открытом состоянии
   - Контроль фактического состояния
   - Автовосстановление при потере позиции

4. **closing**:
   - Проверка безопасности (позиция бурголовки)
   - Подача команды закрытия (значение по параметру `close_ctrl`, по умолчанию отрицательное)
   - Контроль таймаутов и реакции

5. **closed**:
   - Проверка безопасности по позиции бурголовки
   - Автоматическое открытие при превышении
   - Контроль фактического состояния

### Диаграмма переходов

```
    idle
   ↙    ↘
opening  closing
   ↓      ↓
  open   closed
   ↑      ↑
   └──────┘
```

## Логика безопасности

### Проверки безопасности

1. **Позиция бурголовки**: Запрет закрытия при `drill_state.head_pos > max_head_pos_to_close`. Если уже в `closed` и порог превышен — немедленно перейти в `opening` (самозащита).
2. **Валидность данных**: Проверка `drill_state.head_pos_is_reliable`
3. **Консистентность концевиков**: Недопустимо одновременное срабатывание `open` и `closed`
4. **Таймауты операций**: Контроль времени выполнения команд

### События безопасности

- `rc_open_arm` - не удалось открыть люнет
- `rc_close_arm` - не удалось закрыть люнет
- `head_too_low` - бурголовка слишком низко для закрытия
- `switch_failure` - неисправность концевых выключателей

Требуется добавить отсутствующие коды событий в `base_node/base_node/event_codes.py` (или использовать существующие подходящие коды), чтобы их можно было указывать при логировании через `BaseNode.log/handle_error`.

## Параметры конфигурации

Добавить в `params_server/params_server/base_config/nodes.yaml`:

```yaml
ArmController:
  rate: 10.0                    # Частота работы FSM (Гц)
  opening_time: 10.0            # Таймаут открытия (сек)
  closing_time: 8.0             # Таймаут закрытия (сек)
  open_push_time: 1.0           # Время удержания в открытом состоянии (сек)
  close_push_time: 1.0          # Время удержания в закрытом состоянии (сек)
  grip_push_time: 0.5           # Доп. время на «дожим» захвата (сек), если нет датчика
  no_reaction_time: 3.0         # Таймаут отсутствия реакции (сек)
  max_head_pos_to_close: 0.5    # Максимальная позиция бурголовки для закрытия (м)
  arm_present: true             # Флаг наличия люнета в системе
  arm_grip_sensor_present: true # Флаг наличия датчика зажима
  open_ctrl: 1.0                # Команда управления для открытия (знак/величина)
  close_ctrl: -1.0              # Команда управления для закрытия (знак/величина)
  allowed_modes:                # Разрешенные режимы работы
    - "shaft_buildup"
    - "shaft_stow"
  switch_inconsistent_time: 0.3 # (опц.) время для фиксации неконсистентности концевиков (оба активны)
```

Параметры должны поддерживать динамическое обновление через механизм параметров `BaseNode` (наш самописный сервер параметров). Все изменения значений, приходящие с сервера, должны применяться без перезапуска ноды.

## Сообщения

- `drill_msgs/OpenCloseAction` — использовать поля `id:int32` и `action:int32` (значения: `OPEN=1`, `CLOSE=-1`).
- `drill_msgs/ArmState` — требуется добавить в пакет сообщений, если его ещё нет, со структурой:

```text
std_msgs/Header header
bool open
bool closed
bool grip
```

## Структура реализации

### Структура пакета

```
src/controllers/arm_controller/
├── arm_controller/
│   ├── __init__.py
│   ├── arm_controller.py      # Основной FSM класс
│   └── states.py              # Классы состояний
├── launch/
│   └── arm_controller.launch.xml
├── README.md
├── package.xml
└── setup.py
```

### Основной класс FSM

```python
from base_node.base_fsm import BaseFSM
from drill_msgs.msg import ArmState, DrillState, FloatCtrl, OpenCloseAction
from .states import IdleState, OpeningState, OpenState, ClosingState, ClosedState

class ArmControllerFSM(BaseFSM):
    def __init__(self):
        super().__init__(node_name="arm_controller")
        
        # Регистрация состояний
        self.add_states(
            IdleState(self),
            OpeningState(self),
            OpenState(self),
            ClosingState(self),
            ClosedState(self)
        )
        
        # Подписчики с таймаутами
        self.add_subscriber("/arm_state", ArmState, "arm_state", timeout=1.0)
        self.add_subscriber("/drill_state", DrillState, "drill_state", timeout=1.0)

        # Подписка на команды управления
        self.create_subscription(OpenCloseAction, "/arm_action", self.action_callback, 10)

        # Публикатор команд (централизованная публикация)
        self.control_pub = self.create_publisher(FloatCtrl, "/arm_ctrl", 10)
        self._arm_ctrl = 0.0
        
        # Установка начального состояния
        self.set_state("idle")
    
    def stop_control(self):
        """Безопасная остановка люнета"""
        msg = FloatCtrl()
        msg.header.stamp = self.get_rostime()
        msg.ctrl = 0.0
        self.control_pub.publish(msg)
    
    def safety_check(self) -> bool:
        """Проверки безопасности"""
        # Проверка валидности данных бурения
        if self.subs.drill_state is None or not self.subs.drill_state.head_pos_is_reliable:
            return False
            
        # Проверка консистентности концевиков
        if self.subs.arm_state is not None:
            if self.subs.arm_state.open and self.subs.arm_state.closed:
                self.handle_error(
                    "Inconsistent switch state!",
                    level=self.ERROR,
                    event_code=self.events.SWITCH_FAILURE
                )
                return False
                
        return True
    
    def do_work_finally(self):
        """Единая точка публикации управления (как в ROS1 publish_arm_ctrl)."""
        msg = FloatCtrl()
        msg.header.stamp = self.get_rostime()
        msg.ctrl = self._arm_ctrl
        self.control_pub.publish(msg)

    def action_callback(self, msg: OpenCloseAction):
        """Обработка команд управления люнетом"""
        current_state = self.get_current_state_name()

        if msg.action == OpenCloseAction.OPEN and current_state in ["idle", "closed"]:
            self.set_state("opening")
        elif msg.action == OpenCloseAction.CLOSE and current_state in ["idle", "open"]:
            self.set_state("closing")
        else:
            self.log(
                f"Invalid command or state. action={msg.action}, current_state={current_state}"
            )
```

### Классы состояний

Базовая структура состояний с использованием параметров и проверок безопасности.

## Launch файл

```xml
<?xml version="1.0"?>
<launch>
    <!-- Переменные окружения для BaseNode -->
    <set_env name="PARAM_SERVER_ADDRESS" value="localhost" />
    <set_env name="PARAM_SERVER_PORT" value="5000" />
    <set_env name="REPORT_RATE" value="5.0" />
    
    <!-- Запуск контроллера люнета -->
    <node pkg="arm_controller"
          exec="arm_controller"
          name="arm_controller"
          output="screen"
          respawn="true"
          respawn_delay="5" />
</launch>
```

## Критерии приемки

1. **Функциональность**: Корректное открытие/закрытие люнета по командам
2. **Безопасность**: Запрет закрытия при глубоком бурении
3. **Надежность**: Автовосстановление при потере позиции
4. **Диагностика**: Корректная публикация событий и отчетов
5. **Интеграция**: Работа с системой параметров и BaseFSM
6. **Документация**: Полная документация README.md с входами, выходами, параметрами и логикой работы

## Детализация логики состояний (с учётом ROS1-реализации)

- **opening**:
  - Публиковать `FloatCtrl(ctrl=open_ctrl)`.
  - Если спустя `no_reaction_time` остаётся признак «застревания» (при наличии датчика `grip`: `grip==True`, иначе — `closed==True`) — событие `rc_open_arm` и предупреждение.
  - При `open==True` удерживать команду ещё `open_push_time`, затем переход в `open`.

- **open**:
  - Останов управления (`ctrl=0`).
  - Если `open==False` — «восстановление» через переход в `opening`.
  - Если пришла команда закрытия — переход в `closing`.

- **closing**:
  - Если `drill_state.head_pos > max_head_pos_to_close` — событие `head_too_low`, останов и переход в `opening`.
  - Публиковать `FloatCtrl(ctrl=close_ctrl)`.
  - Если по истечении `no_reaction_time` `open==True` — событие `rc_close_arm`.
  - Если `arm_grip_sensor_present==True`: требовать `closed==True && grip==True` непрерывно в течение `close_push_time` для перехода в `closed`.
  - Иначе: требовать `closed==True` непрерывно в течение `grip_push_time + close_push_time` для перехода в `closed`.

- **closed**:
  - Останов управления (`ctrl=0`).
  - Защита по глубине: если `head_pos > max_head_pos_to_close` — переход в `opening`.
  - Если потеряна фиксация (`closed==False` или, при наличии датчика, `grip==False`) — «восстановление» через `closing`.
  - Если пришла команда открытия — переход в `opening`.

Все проверки безопасности должны выполняться в `safety_check()` и через встроенные проверки `BaseFSM` (permission, robomode, таймауты подписчиков). Для временных проверок использовать «чистое» время состояния (`get_current_state_duration()`), если применимо.

## Следующие шаги

После успешной реализации arm_controller использовать его как шаблон для переноса остальных контроллеров (tower, wrench, carousel) с аналогичной архитектурой.
