# CanEncoder

## 1. Назначение и общий контекст

**Задача ноды.** `CanEncoder` формирует CAN‑кадры (`can_msgs/msg/Frame`) из ROS 2 сообщений более высокого уровня. Нода опирается на YAML‑описания протоколов, параметры преобразования значений и заданную частоту публикации.

**Позиция в системе.** Она располагается между прикладной логикой  и драйвером CAN‑шины:

* **Вход** — один или несколько «прикладных» топиков с произвольными ROS 2 сообщениями (например, `geometry_msgs/Twist`, `sensor_msgs/Joy`, или кастомные) **плюс** три служебных канала: `/main_state_machine_status` (`drill_msgs/StateMachineStatus`), `/permission` (`drill_msgs/Permission`) и `/robomode` (`drill_msgs/BoolStamped`). Для каждого прикладного топика автоматически создаётся пара `_remote`, используемая в режиме дистанционного управления.
* **Выход** — топики `/canX` с сообщениями `Frame`, которые читает `nmttcan_interface` или аналогичный драйвер.

**Выполняемые задачи:**

* Приём и буферизация значений из входных топиков.
* Пересчёт значений между диапазонами, используемыми в CAN и ROS2 сообщениях c учетом нелинейности, зон насыщения и нечувствительности.
* Сериализация значений в поля CAN‑кадра, согласно описанному протоколу с поддержкой стандарта J1939.
* Периодическая отправка CAN-сообщений с заданной частотой (несколько частотных групп).
* Автоматический откат к безопасному `safe_value`, если вход теряет актуальность.
* Учет текущего состояния машины: **remote / auto**, разрешение движения, роборежим.

---

## 2. Файлы конфигурации

### 2.1 YAML‑описание протокола CAN

Размещается в пакете `can_encoder` в директории `protocols/`. Файл описывает структуру кадра в секции `data`, а также содержит дополнительные параметры, необходимые для генерации кадра:

| Ключ         | Значение (пример) | Обязательно                   | Описание                                                                         |
| ------------ | ----------------- | ----------------------------- | -------------------------------------------------------------------------------- |
| `id`         | `0x123`           | ✔, если не используется `pgn` | Полный CAN‑ID для классического кадра. Взаимоисключает пару `pgn` + `source_id`. |
| `pgn`        | `0xEF04`          | ✔, если не используется `id`  | Parameter Group Number (J1939, 18 бит).                                          |
| `priority`   | `3`               | ✔, для J1939                  | Приоритет кадра (J1939, 3 бит, 0‒7).                                             |
| `source_id`  | `0x01`            | ✔, для J1939                  | Source Address (J1939, 8 бит).                                                   |
| `dlc`        | `8`               | —                             | Data Length Code; по умолчанию `8`.                                              |
| `byte_order` | `little`          | —                             | Порядок байт (`little` / `big`).                                                 |
| `data`       | dict              | ✔                             | Описание полей кадра (см. ниже).                                                 |

#### Описание поля внутри `data`

| Ключ         | Тип/формат                                     | Обязательно                | Описание                                                                                                         |
| ------------ | ---------------------------------------------- | -------------------------- | ---------------------------------------------------------------------------------------------------------------- |
| `type`       | Строка (см. таблицу поддерживаемых типов ниже) | ✔                          | Определяет, как интерпретировать байты/биты.                                                                     |
| `byte`       | Целое ≥ 1 (1‑based)                            | ✔                          | Индекс первого байта поля в кадре.                                                                               |
| `bit`        | 0‑7                                            | только для `t`, `ti`, `uN` | Бит внутри выбранного байта (0 — LSB).                                                                           |
| `resolution` | Число или строка "N/D"                         | —                          | Итог = raw × resolution + offset. Строка поддерживает рациональные коэффициенты без округления, например, `1/8`. |
| `offset`     | Число                                          | —                          | Смещение, добавляемое после масштабирования.                                                                     |
| любые др.    | люб.                                           | —                          | Игнорируются кодом, но полезны для комментариев (например, `units`, `comment`).                                  |

##### Поддерживаемые типы `type`

| Тип                                           | Интерпретация                                       | Требует `bit` | Примечания                                                                  |
| --------------------------------------------- | --------------------------------------------------- | ------------- | --------------------------------------------------------------------------- |
| `t`                                           | Логический, «истина» если указанный бит = 1         | ✔             | Битовый флаг.                                                               |
| `ti`                                          | Логический, «истина» если бит = 0 (инвертированный) | ✔             | Инвертированный флаг.                                                       |
| `uN`                                          | Беззнаковое поле из **N** бит (1 ≤ N ≤ 8)           | ✔             | Например, `u3` извлекает 3‑битовое значение из указанного байта.            |
| `T`                                           | 24‑бит беззнаковое (`uint24`)                       | —             | Читает **три** последовательных байта с учётом `byte_order`.                |
| форматы `struct` (`B`, `H`, `I`, `f`, и т.д.) | Любой поддержанный символ модуля `struct`           | —             | Используется как `struct.pack_into`/`unpack_from` с префиксом порядка байт. |

> **Замечание:** для многобайтовых типов порядок байт контролируется атрибутом `byte_order` в верхней части YAML‑файла.

### 2.2 Параметры ноды

```yaml
can_encoder:
  values:  # обрабатываемые значения (например, целевые обороты двигателя)      
    <logical_value_name>:
      safe_value: <number>              # безопасное значение по умолчанию
      timeout:    <sec>                 # max возраст данных до установки safe_value
      # --- опциональные настройки для положительного диапазона (вход > 0) ---
      input_max_positive:         <num> # максимальное значение на входе
      output_saturation_positive: <num> # максимальное значение на выходе
      output_deadband_positive:   <num> # минимальное значение на выходе при ненулевом входе
      polynomial_pos: [c0, c1, …]      # коэффициенты полинома для компенсации нелинейности
      # --- опциональные настройки для отрицательного диапазона (вход < 0), симметрично положительным ---
      input_max_negative:         <num>
      output_saturation_negative: <num>
      output_deadband_negative:   <num>
      polynomial_neg: [c0, c1, …]
      # --- сервисные поля ---
      deadband_thr: <num>              # порог для определния нулевого входа; по умолчанию 0.001

  input_topics:  # Что принимаем от верхнеуровневой системы
    <topic_name>:
      msg_type: <package/MsgType>      # пример: geometry_msgs/Twist
      fields:                          # сопоставление "поле сообщения → logical_value"
        <ros_field_path>: <logical_value_name>

  can_messages: # Что публикуем на шину
    <can_message_key>:
      protocol: <protocol_yaml_basename>
      channel:  <can_frame_topic>       # напр. /can0
      rate:     <Hz>                    # частота публикации (целое > 0)
      fields:                           # сопоставление "поле CAN → logical_value"
        <can_field_name>: <logical_value_name>
```

#### Блок `values`

| Поле                         | Тип      | Обяз. | Описание                                                          |
| ---------------------------- | -------- | ----- | ----------------------------------------------------------------- |
| `safe_value`                 | число    | ✔     | Подставляется, если вход устарел или вышел за пределы.            |
| `timeout`                    | число    | ✔     | Лимит устаревания (сек).                                          |
| `input_max_positive`         | число >0 | —     | Макс. значение входа для положительного диапазона.                |
| `output_saturation_positive` | число >0 | —     | Верхняя граница выхода (> deadband).                              |
| `output_deadband_positive`   | число >0 | —     | Мёртвая зона между 0 и началом линейной части.                    |
| `polynomial_pos`             | массив   | —     | Коэфф. полинома `np.polynomial.Polynomial` для нелинейной кривой. |
| `input_max_negative`         | число <0 | —     | Аналогично для отриц. диапазона.                                  |
| `output_saturation_negative` | число <0 | —     | —                                                                 |
| `output_deadband_negative`   | число <0 | —     | —                                                                 |
| `polynomial_neg`             | массив   | —     | —                                                                 |
| `deadband_thr`               | число >0 | —     | Абсолютный порог «нулевых» колебаний входа (по умолчанию 0.001).  |

> Если ни одного блока `*_positive/negative` не задано, вход передаётся без преобразований.

#### Блок `input_topics`

| Поле       | Тип    | Обяз. | Описание                           |
| ---------- | ------ | ----- | ---------------------------------- |
| `msg_type` | string | ✔     | Тип входного ROS‑сообщения.        |
| `fields`   | dict   | ✔     | Путь в сообщении → логическое имя. |

Для каждого  топика `topic_name` нода автоматически подписывается и на `${topic_name}_remote`. Второй используется только если `/main_state_machine_status.current_state == 'remote'`.

#### Блок `can_messages`

| Поле       | Тип    | Обяз. | Описание                                                                           |
| ---------- | ------ | ----- | ---------------------------------------------------------------------------------- |
| `protocol` | string | ✔     | имя YAML-файла протокола; определяет структуру кадра.                              |
| `channel`  | string | ✔     | ROS‑топик `/canX`, куда публикуется `Frame`.                                       |
| `rate`     | int    | ✔     | Частота отправки (Hz). Сообщения с одинаковой частотой объединяются в один таймер. |
| `fields`   | dict   | ✔     | Сопоставление «поле CAN → logical\_value».                                         |

##### Минимальный рабочий пример

```yaml
can_encoder:
  values:
    throttle:
      safe_value: 0
      timeout: 0.1
      input_max_positive: 1.0
      output_saturation_positive: 100
  input_topics:
    /cmd/throttle:
      msg_type: std_msgs/Float32
      fields:
        data: throttle
  can_messages:
    eng_ctrl:
      protocol: engine_ctrl
      channel: /can0
      rate: 50
      fields:
        THROTTLE_CMD: throttle
```

##### Комплексный пример с двумя частотами

```yaml
can_encoder:
  values:
    steer:   {safe_value: 0, timeout: 0.05, input_max_positive: 1.0, output_saturation_positive: 450 }
    brake:   {safe_value: 0, timeout: 0.05, input_max_positive: 1.0, output_saturation_positive: 200 }
    horn:    {safe_value: 0, timeout: 0.5 }

  input_topics:
    /cmd/steer:  {msg_type: std_msgs/Float32, fields: {data: steer}}
    /cmd/brake:  {msg_type: std_msgs/Float32, fields: {data: brake}}
    /vehicle/horn: {msg_type: std_msgs/Bool,   fields: {data: horn}}

  can_messages:
    veh_ctrl_fast:
      protocol: veh_fast
      channel: /can0
      rate: 100
      fields: { STEER: steer, BRAKE: brake }

    veh_ctrl_slow:
      protocol: veh_slow
      channel: /can0
      rate: 10
      fields: { HORN: horn }
```
---

## 3. Алгоритм работы

### 3.1 Буферизация входных значений

1. При подписке на `input_topics` создаётся callback `all_topics_callback` (по одному на «обычный» и «\_remote» вариант).
2. В callback:

   * Проверяется, совпадает ли режим (`remote` ↔ не `remote`) и принадлежит ли сообщение нужному каналу.
   * Полученное значение проходит `transform_input()`:

     * **deadband\_thr** — срезает мелкие колебания.
     * **Нормализация** `normalized = value / input_max`.
     * **Полиномиальная коррекция** `poly(normalized)` (если задана).
     * **Линейное растяжение** к диапазону `output_deadband…saturation`.
     * **Зеркальное отражение** для отрицательной ветки.
   * Результат пишется в `self.values[val_name]['value']` и маркируется меткой времени.

### 3.2 Сериализаторы CAN

При инициализации:

* Для каждого YAML‑протокола формируется объект `CANSerializer`.
* Внутри `CANSerializer` для каждого поля строится **setter‑лямбда**, которая кодирует значение в соответствии с описанием протокола и помещает его в нужные биты/байты с учётом порядка, масштаба и смещения:

  * Применяется обратное масштабирование `(val - offset) / resolution`.
  * Для битовых/полубайтных полей — маскирование, проверка диапазона.
  * Для многобайтовых — `struct.pack_into` с учётом `byte_order`.
* Итоговый `Frame` получает:

  * `id` = `frame_id` **или** `(priority<<26 | pgn<<8 | source_id)`.
  * `is_extended` определяется автоматически.
  * `dlc` берётся из YAML (`8` по умолчанию).

### 3.3 Таймеры публикации

* CAN‑сообщения группируются по частоте `rate`; для каждой создаётся `create_timer(1/rate, ...)`.
* В `timer_callback(rate)` на текущем ROS‑времени:

  1. Перебираются все `can_messages` этой частоты.
  2. Для каждого поля сообщения берётся актуальное значение **если** `now - last_ts < timeout`, иначе ‑ `safe_value`.
  3. `CANSerializer.serialize(msg_data)` создаёт `Frame`.
  4. `msg.header.stamp = self.get_rostime()`.
  5. Сообщение публикуется паблишером, ассоциированным с `channel`.

### 3.4 Режимы и разрешения

* **/main\_state\_machine\_status** (`StateMachineStatus`) задаёт `self.main_mode` — строка `'remote'` включает работу с топиками `*_remote`, любая другая - с обычными.
* **/permission** (`Permission`) — флаг `move_permission` - при получении `False` всем выходным сообщениям устанавливаются безопасные значения до тех пор, пока не будет получено True.
* **/robomode** (`BoolStamped`) — сохраняется в `self.robomode`,  аналогично permission.
* `all_topics_callback` пропускает сообщение, если оно относится к «чужому» режиму (remote ↔ auto).

---

## 4. Сообщения об ошибках и валидация

| Где возникает                        | Условие / причина                        | Сообщение / исключение                                       | Уровень / тип             |
| ------------------------------------ | ---------------------------------------- | ------------------------------------------------------------ | ------------------------- |
| Загрузка протоколов                  | YAML‑файл не найден                      | `Protocol file '<path>' not found`                           | `self.ERROR` (`SW_ERROR`) |
| Импорт ROS‑сообщений                 | Неверный `package/MsgType`               | `Message import error <type>: <exception>`                   | `self.ERROR`              |
| Валидация `CANSerializer`            | Нет `data` или `dlc`                     | `ValueError` с пояснением                                    | Исключение                |
| Неполный J1939 набор                 | Нет `pgn` / `priority` / `source_id`     | `ValueError: For J1939 frame ... must be provided`           | Исключение                |
| Описание поля без `byte`             | —                                        | `ValueError: Field '<name>' must include 'byte' parameter`   | Исключение                |
| Нет `bit` для `t`, `ti`, `uN`        | —                                        | `ValueError: Field '<name>' of type '<type>' requires 'bit'` | Исключение                |
| Выход за маску `uN`                  | raw не помещается                        | `ValueError: Value <val> for field '<name>' is out of range` | Исключение                |
| Неподдерживаемый `type`              | —                                        | `ValueError: Unsupported type '<type>' for field '<name>'`   | Исключение                |
| Ошибка диапазона входного значения   | > `input_max` или < `input_max_negative` | `Value <name> is out of range: <val>!`                       | `self.ERROR` (periodic)   |
| Некорректная конфигурация шкалы      | deadband ≥ saturation                    | `Deadband for <name> is higher or equal to saturation!`      | `self.WARN` (periodic)    |
| Не найдено поле во входном сообщении | `getattr(msg, field)` вернул `None`      | `Message in '<topic>' does not contain field <field>`        | `self.ERROR`              |
