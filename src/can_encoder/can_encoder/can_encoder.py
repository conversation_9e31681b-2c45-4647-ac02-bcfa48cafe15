import numpy as np
import rclpy
from base_node.base_node import BaseNode
from drill_msgs.msg import StateMachineStatus, Permission, BoolStamped
from can_msgs.msg import Frame

import struct
import importlib

import os
import yaml
from ament_index_python.packages import get_package_share_directory


class CANSerializer:
    def __init__(self, frame_id=None, pgn=None, data=None, byte_order="little", dlc=None, priority=None,
                 source_id=None):
        """
        Initializes the CANSerializer instance.

        :param frame_id: Explicit frame identifier, if available.
        :param pgn: Parameter Group Number for J1939 frame_id generation.
        :param data: Dictionary with field definitions.
        :param byte_order: Byte order ("little" or "big").
        :param dlc: Data Length Code (number of data bytes, typically 8).
        :param priority: Priority value used in J1939 frame_id generation.
        :param source_id: Source identifier used in J1939 frame_id generation.
        """
        if data is None:
            raise ValueError("Data dictionary with field definitions is required")
        if dlc is None:
            raise ValueError("DLC (data length code) must be provided")
        self.dlc = dlc
        self.byte_order = byte_order
        self.data_definitions = data  # Store field definitions for later use

        # Compute the CAN identifier and is_extended flag
        if frame_id is not None:
            self.can_id = frame_id
            self.is_extended = (frame_id > 0x7FF)
        else:
            if pgn is None or priority is None or source_id is None:
                raise ValueError(
                    "For J1939 frame, pgn, priority, and source_id must be provided if frame_id is not given")
            self.can_id = (priority << 26) | (pgn << 8) | source_id
            self.is_extended = True  # J1939 frames are always extended

        # Define struct format prefix based on byte order
        order_prefix = "<" if byte_order == "little" else ">"
        self.fields = {}

        # Process each field definition to create corresponding setter functions
        for field_name, cfg in data.items():
            field_type = cfg.get("type")
            byte_index = cfg.get("byte")
            if byte_index is None:
                raise ValueError(f"Field '{field_name}' must include 'byte' parameter")
            adjusted_byte = byte_index - 1

            bit = cfg.get("bit")
            resolution = cfg.get("resolution", 1)

            if type(resolution) is str:
                res = resolution.split('/')
                resolution = int(res[0]) / int(res[1])

            offset = cfg.get("offset", 0)

            if field_type in ("t", "ti"):
                if bit is None:
                    raise ValueError(f"Field '{field_name}' of type '{field_type}' requires 'bit'")
                if field_type == "t":
                    def setter(frame_data, val, byte_idx=adjusted_byte, bit_val=bit):
                        frame_data[byte_idx] = (frame_data[byte_idx] & ~(1 << bit_val)) | ((1 if val else 0) << bit_val)

                    self.fields[field_name] = setter
                else:  # "ti" inverted boolean
                    def setter(frame_data, val, byte_idx=adjusted_byte, bit_val=bit):
                        frame_data[byte_idx] = (frame_data[byte_idx] & ~(1 << bit_val)) | ((0 if val else 1) << bit_val)

                    self.fields[field_name] = setter

            elif field_type.startswith("u") and field_type[1:].isdigit():
                bits_count = int(field_type[1:])
                if bit is None:
                    raise ValueError(f"Field '{field_name}' of type '{field_type}' requires 'bit'")
                mask = (1 << bits_count) - 1

                def setter(frame_data, val, byte_idx=adjusted_byte, bit_val=bit, mask_val=mask,
                           r_val=resolution, off_val=offset, fname=field_name):
                    # Reverse the scaling: (val - offset) / resolution, rounded to the nearest integer.
                    raw = int(round((val - off_val) / r_val))
                    if raw < 0 or raw > mask_val:
                        raise ValueError(f"Value {val} for field '{fname}' is out of range")
                    # Clear the target bits
                    frame_data[byte_idx] &= ~(mask_val << bit_val)
                    # Set the target bits with the computed raw value
                    frame_data[byte_idx] |= (raw & mask_val) << bit_val

                self.fields[field_name] = setter

            elif field_type in ("B", "H", "I") or (len(field_type) == 1):
                fmt = order_prefix + field_type
                struct_obj = struct.Struct(fmt)

                def setter(frame_data, val, byte_idx=adjusted_byte, struct_object=struct_obj, r_val=resolution,
                           off_val=offset):
                    # Reverse scaling: (val - offset) / resolution, then convert to integer
                    raw = int(round((val - off_val) / r_val))
                    struct_object.pack_into(frame_data, byte_idx, raw)

                self.fields[field_name] = setter
            else:
                raise ValueError(f"Unsupported type '{field_type}' for field '{field_name}'")

    def serialize(self, fields_data):
        """
        Serializes the provided field values into a ROS2 CAN frame message (drill_msgs/CanFrame).

        :param fields_data: Dictionary containing values for all defined fields.
        :return: An instance of drill_msgs/CanFrame with the serialized data.
        """
        # Create a bytearray of 8 bytes initialized to zero
        frame_data = bytearray(8)

        # For each field, call the corresponding setter function to write the value into frame_data
        for field_name, setter in self.fields.items():
            val = fields_data.get(field_name, 0)
            try:
                setter(frame_data, val)
            except Exception as e:
                raise ValueError(f"Error setting field '{field_name}': {e}")

        # Create the CAN frame message
        msg = Frame()
        msg.id = self.can_id
        msg.is_rtr = False  # Remote Transmission Request flag (default to False)
        msg.is_extended = self.is_extended
        msg.is_error = False  # Error flag (default to False)
        msg.dlc = self.dlc
        # Convert the bytearray to a list of uint8 values
        msg.data = list(frame_data)
        return msg


class CanEncoder(BaseNode):
    def __init__(self):
        super().__init__('can_encoder')
        # print(f"params: {self.node_params}")
        self.can_serializers = {}
        self.values = {}
        self.can_publishers = {}
        self.can_timers = {}
        self.timer2message_map = {}
        self.drill_subscribers = {}
        self.main_mode = None
        self.main_mode_sub = None
        self.move_permission = True
        self.move_permission_sub = None
        self.robomode = False
        self.robomode_sub = None
        package_name = "can_encoder"
        share_dir = get_package_share_directory(package_name)
        protocol_dir = os.path.join(share_dir, "protocols")

        self.load_values_params()

        protocols = {}
        for can_message_key, can_message_params in self.node_params['can_messages'].items():
            protocol_name = can_message_params['protocol']
            if protocol_name not in protocols:
                protocol_file = os.path.join(protocol_dir, f"{protocol_name}.yaml")
                try:
                    with open(protocol_file, 'r') as f:
                        protocols[protocol_name] = yaml.safe_load(f)
                except FileNotFoundError:
                    self.log(f"Protocol file '{protocol_file}' not found", level=self.ERROR,
                             event_code=self.events.SW_ERROR)
                    raise

            if protocol_name not in self.can_serializers:
                p = protocols[protocol_name]
                self.can_serializers[protocol_name] = CANSerializer(
                    frame_id=p.get('id'),
                    pgn=p.get('pgn'),
                    data=p.get('data'),
                    byte_order=p.get('byte_order', "little"),
                    dlc=p.get('dlc', 8),
                    priority=p.get('priority'),
                    source_id=p.get('source_id')
                )

            for val_name, entry in self.values.items():
                params = entry['params']
                db_pos = params.get('output_deadband_positive')
                sat_pos = params.get('output_saturation_positive')
                if db_pos is not None and sat_pos is not None and db_pos >= sat_pos:
                    raise ValueError(f"Deadband for {val_name} is higher or equal to saturation!")

                db_neg = params.get('output_deadband_negative')
                sat_neg = params.get('output_saturation_negative')
                if db_neg is not None and sat_neg is not None and db_neg <= sat_neg:
                    raise ValueError(f"Deadband for {val_name} is higher or equal to saturation!")

            channel = can_message_params['channel']
            rate = can_message_params['rate']
            if channel not in self.can_publishers:
                self.can_publishers[channel] = self.create_publisher(Frame, channel, 10)

            if rate not in self.timer2message_map:
                self.timer2message_map[rate] = []
            self.timer2message_map[rate].append(can_message_key)

            if rate not in self.can_timers:
                self.can_timers[rate] = self.create_timer(1.0 / rate, lambda r=rate: self.timer_callback(r))

    def load_values_params(self):
        for val_name, val_params in self.node_params['values'].items():
            self.values[val_name] = {
                'value': val_params['safe_value'],
                'last_ts': 0,
                'params': val_params
            }
            if 'polynomial_pos' in val_params:
                self.values[val_name]['params']['_polynomial_func_pos'] = np.polynomial.polynomial.Polynomial(
                    val_params['polynomial_pos'])
            if 'polynomial_neg' in val_params:
                self.values[val_name]['params']['_polynomial_func_neg'] = np.polynomial.polynomial.Polynomial(
                    val_params['polynomial_neg'])

    def on_params_update(self, interesting_updated_keys):
        if self.get_name() in interesting_updated_keys:
            self.load_values_params()

    def timer_callback(self, rate):
        t = self.get_time()
        rt = self.get_rostime()
        for msg_key in self.timer2message_map[rate]:
            msg_data = {}
            msg_params = self.node_params['can_messages'][msg_key]
            for field, val_name in msg_params['fields'].items():
                val_params = self.values[val_name]['params']
                if t - self.values[val_name]['last_ts'] < val_params['timeout']:
                    msg_data[field] = self.values[val_name]['value']
                else:
                    msg_data[field] = val_params['safe_value']
            msg = self.can_serializers[msg_params['protocol']].serialize(msg_data)
            msg.header.stamp = rt
            self.can_publishers[msg_params['channel']].publish(msg)

    def main_mode_cb(self, msg):
        self.main_mode = msg.current_state

    def permission_cb(self, msg):
        self.move_permission = msg.permission

    def robomode_cb(self, msg):
        self.robomode = msg.value

    def transform_input(self, value, params, val_name):
        deadband_thr = params.get('deadband_thr', 0.001)

        # Immediately return zero if within deadband threshold
        if abs(value) < abs(deadband_thr):
            return 0

        if value > 0:
            input_max = params.get('input_max_positive', 0)
            saturation = params.get('output_saturation_positive')
            if input_max <= 0:
                self.log(f"Invalid or unset input_max_positive for {val_name}!", level=self.ERROR,
                         event_code=self.events.SW_ERROR, period=1)
                return params.get('safe_value', 0)
            if saturation is None:
                self.log(f"Unset output_saturation_positive for {val_name}!", level=self.ERROR,
                         event_code=self.events.SW_ERROR, period=1)
                return params.get('safe_value', 0)
            if value > input_max:
                self.log(f"Value {val_name} is out of range: {value}!", level=self.ERROR,
                         event_code=self.events.SW_ERROR, period=1)
                return params.get('safe_value', 0)

            result = self._transform_input_general(
                value=value,
                input_max=input_max,
                deadband=params.get('output_deadband_positive', 0),
                saturation=saturation,
                poly_func=params.get('_polynomial_func_pos'),
                valname=val_name
            )
            return result

        if value < 0:
            input_max = params.get('input_max_negative', 0)
            saturation = params.get('output_saturation_negative')
            if input_max >= 0:
                self.log(f"Invalid or unset input_max_negative for {val_name}!", level=self.ERROR,
                         event_code=self.events.SW_ERROR, period=1)
                return params.get('safe_value', 0)
            if saturation is None:
                self.log(f"Unset output_saturation_negative for {val_name}!", level=self.ERROR,
                         event_code=self.events.SW_ERROR, period=1)
                return params.get('safe_value', 0)
            if value < input_max:
                self.log(f"Value {val_name} is out of range: {value}!", level=self.ERROR,
                         event_code=self.events.SW_ERROR, period=1)
                return params.get('safe_value', 0)

            result = self._transform_input_general(
                value=value,
                input_max=input_max,
                deadband=params.get('output_deadband_negative', 0),
                saturation=saturation,
                poly_func=params.get('_polynomial_func_neg'),
                valname=val_name
            )
            return -result

        return 0

    def _transform_input_general(self, value, input_max, deadband, saturation, poly_func, valname):
        normalized_value = value / input_max

        deadband = abs(deadband)
        saturation = abs(saturation)
        out_range = max(saturation - deadband, 0)

        if out_range == 0:
            self.log(f"Deadband for {valname} is higher or equal to saturation!", level=self.WARN, period=1)

        if callable(poly_func):
            normalized_value = poly_func(normalized_value)

        out = normalized_value * out_range

        if out != 0:
            out += deadband

        # Clamping to maximum allowed value
        return min(out, out_range + deadband)

    def all_topics_callback(self, msg, topic_name, is_remote):
        veh_in_remote = self.main_mode == 'remote'
        if is_remote:
            base_topic_name = topic_name.removesuffix("_remote")
        else:
            base_topic_name = topic_name

        if self.node_params['make_remote_topics'] and not \
            self.node_params['input_topics'][base_topic_name].get("same_in_remote", False) and \
            veh_in_remote != is_remote:
            return
        t = self.get_time()

        # Strip "_remote" suffix from topic_name for parameter lookup

        for field, val_name in self.node_params['input_topics'][base_topic_name]['fields'].items():
            val_params = self.values[val_name]['params']
            in_val = getattr(msg, field, None)
            if in_val is None:
                self.log(f"Message in '{topic_name}' does not contain field {field}", level=self.ERROR,
                         event_code=self.events.SW_ERROR)
                continue
            if self.move_permission and self.robomode:
                if 'input_max_positive' in val_params or 'input_max_negative' in val_params:
                    out_val = self.transform_input(in_val, val_params, val_name)
                else:
                    out_val = in_val
            else:
                out_val = val_params['safe_value']
            self.values[val_name]['value'] = out_val
            self.values[val_name]['last_ts'] = t

    def initialize(self):
        self.main_mode_sub = self.create_subscription(StateMachineStatus, "/main_state_machine_status",
                                                      self.main_mode_cb, 10)
        self.move_permission_sub = self.create_subscription(Permission, "/permission", self.permission_cb, 10)
        self.robomode_sub = self.create_subscription(BoolStamped, "/robomode", self.robomode_cb, 10)

        for topic_name, topic_params in self.node_params['input_topics'].items():
            package, msg_name = topic_params['msg_type'].split('/')
            module_name = f"{package}.msg"
            try:
                mod = importlib.import_module(module_name)
                msg_class = getattr(mod, msg_name)
            except (ModuleNotFoundError, AttributeError) as e:
                self.log(f"Message import error {topic_params['msg_type']}: {e}", level=self.ERROR)
                raise

            # check msg fields
            msg_inst = msg_class()
            for field_path in topic_params['fields']:
                current = msg_inst
                for attr in field_path.split('.'):
                    if not hasattr(current, attr):
                        raise AttributeError(
                            f"Field '{field_path}' is not defined in message {topic_params['msg_type']}"
                        )
                    current = getattr(current, attr)

            self.drill_subscribers[topic_name] = self.create_subscription(
                msg_class,
                topic_name,
                lambda msg, topic=topic_name, is_remote=False: self.all_topics_callback(msg, topic, is_remote),
                10
            )
            if self.node_params['make_remote_topics'] and not topic_params.get("same_in_remote", False):
                remote_topic_name = f"{topic_name}_remote"
                self.drill_subscribers[remote_topic_name] = self.create_subscription(
                    msg_class,
                    remote_topic_name,
                    lambda msg, topic=remote_topic_name, is_remote=True: self.all_topics_callback(msg, topic, is_remote),
                    10
                )

def main():
    rclpy.init()
    node = CanEncoder()
    node.run()


if __name__ == '__main__':
    main()
