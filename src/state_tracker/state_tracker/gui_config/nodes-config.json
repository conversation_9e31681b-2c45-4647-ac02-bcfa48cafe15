{"io": {"Subscriber": {"params": {"topic_name": "", "queue_size": 10, "process_rate": null}, "allow_add_inputs": false, "allow_add_outputs": true, "inputs": [], "outputs": []}, "Publisher": {"params": {"topic_name": "", "limit_period": null, "queue_size": 10}, "allow_add_inputs": true, "allow_add_outputs": false, "inputs": [], "outputs": []}, "TransformPublisher": {"params": {"from_frame": "", "to_frame": "", "reverse": 0}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "Transform", "type": "geometry_msgs/TransformStamped", "allow_add_channels": false, "channels": [{"name": "", "type": "header.stamp"}, {"name": "", "type": "transform.translation.x"}, {"name": "", "type": "transform.translation.y"}, {"name": "", "type": "transform.translation.z"}, {"name": "", "type": "transform.rotation.x"}, {"name": "", "type": "transform.rotation.y"}, {"name": "", "type": "transform.rotation.z"}, {"name": "", "type": "transform.rotation.w"}]}], "outputs": []}}, "basic": {"Mix": {"params": {}, "allow_add_inputs": true, "allow_add_outputs": true, "inputs": [], "outputs": [], "mixtable": true}, "CheckDeviation": {"params": {"allowed_deviation": "", "ang_unit": null}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "In_1", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}, {"name": "In_2", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}], "outputs": [{"name": "<PERSON><PERSON>", "type": "Bool", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}]}, "LPF": {"params": {"cutoff_freq": 10, "ang_unit": null}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "InValue", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}, {"name": "Timestamp", "type": "builtin_interfaces/Time", "allow_add_channels": false, "channels": []}], "outputs": [{"name": "OutValue", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}]}, "LogicalOr": {"params": {}, "allow_add_inputs": true, "allow_add_outputs": false, "inputs": [{"name": "", "type": "Bool", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}, {"name": "", "type": "Bool", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}], "outputs": [{"name": "", "type": "Bool", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}]}, "LogicalAnd": {"params": {}, "allow_add_inputs": true, "allow_add_outputs": false, "inputs": [{"name": "", "type": "Bool", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}, {"name": "", "type": "Bool", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}], "outputs": [{"name": "", "type": "Bool", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}]}, "EulerToQuaternion": {"params": {"input_is_radian": false}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "<PERSON>uler", "type": "Vector3d", "allow_add_channels": false, "channels": [{"name": "roll", "type": "x"}, {"name": "pitch", "type": "y"}, {"name": "yaw", "type": "z"}]}], "outputs": [{"name": "Quaternion", "type": "Quaternion", "allow_add_channels": false, "channels": [{"name": "", "type": "x"}, {"name": "", "type": "y"}, {"name": "", "type": "z"}, {"name": "", "type": "w"}]}]}, "TransformPose": {"params": {"from_frame": "", "to_frame": ""}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "In pose", "type": "geometry_msgs/Pose", "allow_add_channels": false, "channels": [{"name": "", "type": "position.x"}, {"name": "", "type": "position.y"}, {"name": "", "type": "position.z"}, {"name": "", "type": "orientation.x"}, {"name": "", "type": "orientation.y"}, {"name": "", "type": "orientation.z"}, {"name": "", "type": "orientation.w"}]}, {"name": "<PERSON><PERSON>", "type": "builtin_interfaces/Time", "allow_add_channels": false, "channels": []}], "outputs": [{"name": "Out pose", "type": "geometry_msgs/Pose", "allow_add_channels": false, "channels": [{"name": "", "type": "position.x"}, {"name": "", "type": "position.y"}, {"name": "", "type": "position.z"}, {"name": "", "type": "orientation.x"}, {"name": "", "type": "orientation.y"}, {"name": "", "type": "orientation.z"}, {"name": "", "type": "orientation.w"}]}]}, "TransformPoseNoLookUp": {"params": {}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "In pose", "type": "geometry_msgs/Pose", "allow_add_channels": false, "channels": [{"name": "", "type": "position.x"}, {"name": "", "type": "position.y"}, {"name": "", "type": "position.z"}, {"name": "", "type": "orientation.x"}, {"name": "", "type": "orientation.y"}, {"name": "", "type": "orientation.z"}, {"name": "", "type": "orientation.w"}]}, {"name": "Transform", "type": "geometry_msgs/TransformStamped", "allow_add_channels": false, "channels": [{"name": "", "type": "header.stamp"}, {"name": "", "type": "transform.translation.x"}, {"name": "", "type": "transform.translation.y"}, {"name": "", "type": "transform.translation.z"}, {"name": "", "type": "transform.rotation.x"}, {"name": "", "type": "transform.rotation.y"}, {"name": "", "type": "transform.rotation.z"}, {"name": "", "type": "transform.rotation.w"}]}], "outputs": [{"name": "Out pose", "type": "geometry_msgs/Pose", "allow_add_channels": false, "channels": [{"name": "", "type": "position.x"}, {"name": "", "type": "position.y"}, {"name": "", "type": "position.z"}, {"name": "", "type": "orientation.x"}, {"name": "", "type": "orientation.y"}, {"name": "", "type": "orientation.z"}, {"name": "", "type": "orientation.w"}]}]}, "EqualTo": {"params": {"value": 0}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "In", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}], "outputs": [{"name": "Out", "type": "Bool", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}]}, "TimeDerivative": {"params": {"ang_unit": null}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "InValue", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}, {"name": "Timestamp", "type": "builtin_interfaces/Time", "allow_add_channels": false, "channels": []}], "outputs": [{"name": "OutValue", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}]}, "RotateVector": {"params": {"ang_unit": "rad", "cw": 0}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "InVector", "type": "Vector2d", "allow_add_channels": false, "channels": [{"name": "", "type": "x"}, {"name": "", "type": "y"}]}, {"name": "<PERSON><PERSON>", "type": "Float", "allow_add_channels": false, "channels": [{"name": "", "type": "data"}]}], "outputs": [{"name": "OutVector", "type": "Vector2d", "allow_add_channels": false, "channels": [{"name": "", "type": "x"}, {"name": "", "type": "y"}]}]}, "LinearTransform": {"params": {"raw_1": 0.0, "result_1": 0.0, "raw_2": 1.0, "result_2": 1.0}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "In", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}], "outputs": [{"name": "Out", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}]}, "InRange": {"params": {"min_val": null, "max_val": null}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "In", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}], "outputs": [{"name": "Out", "type": "Bool", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}]}, "Limiter": {"params": {"min_val": null, "max_val": null}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "In", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}], "outputs": [{"name": "Out", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}]}, "BasicMath": {"params": {"operation": "+"}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "In1", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}, {"name": "In2", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}], "outputs": [{"name": "Out", "type": "Float", "allow_add_channels": false, "channels": [{"name": "value", "type": "data"}]}]}}, "dedicated": {"RPAlign": {"params": {"swap_rp": false, "roll_offset": 0, "pitch_offset": 0, "invert_roll": false, "invert_pitch": false}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "In", "type": "Vector2d", "allow_add_channels": false, "channels": [{"name": "roll", "type": "x"}, {"name": "pitch", "type": "y"}]}], "outputs": [{"name": "Out", "type": "Vector2d", "allow_add_channels": false, "channels": [{"name": "roll", "type": "x"}, {"name": "pitch", "type": "y"}]}]}, "LatLonToCartesian": {"params": {"utm_zone": 0, "ref_lat": null, "ref_lon": null}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "Geo", "type": "Vector2d", "allow_add_channels": false, "channels": [{"name": "lat", "type": "y"}, {"name": "lon", "type": "x"}]}], "outputs": [{"name": "<PERSON><PERSON><PERSON>", "type": "Vector2d", "allow_add_channels": false, "channels": [{"name": "", "type": "x"}, {"name": "", "type": "y"}]}]}, "ProcessRtkHeading": {"params": {"main_antenna_x": 0, "main_antenna_y": 0, "vect_antenna_x": 0, "vect_antenna_y": 0}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "Raw", "type": "Float", "allow_add_channels": false, "channels": [{"name": "Heading", "type": "data"}]}], "outputs": [{"name": "Processed", "type": "Float", "allow_add_channels": false, "channels": [{"name": "Heading", "type": "data"}]}]}, "CalcWheelsSpeed": {"params": {"width": 1}, "allow_add_inputs": false, "allow_add_outputs": false, "inputs": [{"name": "Speed", "type": "Float", "allow_add_channels": false, "channels": [{"name": "", "type": "data"}]}, {"name": "AngularSpeed", "type": "Float", "allow_add_channels": false, "channels": [{"name": "", "type": "data"}]}], "outputs": [{"name": "Left", "type": "Float", "allow_add_channels": false, "channels": [{"name": "", "type": "data"}]}, {"name": "Right", "type": "Float", "allow_add_channels": false, "channels": [{"name": "", "type": "data"}]}]}}}