import rclpy
from base_node.base_node import BaseNode
from can_msgs.msg import Frame

import struct
import importlib

import os
import yaml
from ament_index_python.packages import get_package_share_directory


def extract_j1939(frame_id):
    dp = (frame_id >> 24) & 0x01  # Data Page
    pf = (frame_id >> 16) & 0xFF  # PDU Format
    ps = (frame_id >> 8) & 0xFF  # PDU Specific
    src = frame_id & 0xFF  # Source Address

    if pf < 240:
        pgn_extracted = (dp << 16) | (pf << 8)
    else:
        pgn_extracted = (dp << 16) | (pf << 8) | ps
    return src, pgn_extracted


def set_nested_attr(obj, attr_path, value):
    attrs = attr_path.split('.')
    for attr in attrs[:-1]:
        obj = getattr(obj, attr)

    field_name = attrs[-1]
    field_type_str = obj._fields_and_field_types[field_name]
    if field_type_str.startswith('int') or field_type_str.startswith('uint'):
        value = int(value)
    elif field_type_str.startswith('float') or field_type_str.startswith('double'):
        value = float(value)
    elif field_type_str.startswith('bool'):
        value = bool(value)
    setattr(obj, attrs[-1], value)


class CANDeserializer:
    def __init__(self, frame_id=None, pgn=None, data=None, byte_order="little"):
        if data is None:
            raise ValueError("Data dictionary with field definitions is required")
        self.frame_id = frame_id
        self.pgn = pgn
        self.byte_order = byte_order

        order_prefix = "<" if byte_order == "little" else ">"
        self.fields = {}

        for field_name, cfg in data.items():
            field_type = cfg.get("type")
            byte_index = cfg.get("byte")
            if byte_index is None:
                raise ValueError(f"Field '{field_name}' must include 'byte' parameter")

            adjusted_byte = byte_index - 1

            bit = cfg.get("bit")
            resolution = cfg.get("resolution", 1)
            if type(resolution) is str:
                res = resolution.split('/')
                resolution = int(res[0]) / int(res[1])

            offset = cfg.get("offset", 0)

            if field_type in ("t", "ti"):
                if bit is None:
                    raise ValueError(f"Field '{field_name}' of type '{field_type}' requires 'bit'")
                if field_type == "t":
                    self.fields[field_name] = lambda frame_data, byte_idx=adjusted_byte, bit_val=bit: bool(
                        (frame_data[byte_idx] >> bit_val) & 0x1)
                else:
                    self.fields[field_name] = lambda frame_data, byte_idx=adjusted_byte, bit_val=bit: not bool(
                        (frame_data[byte_idx] >> bit_val) & 0x1)
            elif field_type.startswith("u") and field_type[1:].isdigit():
                bits_count = int(field_type[1:])
                if bit is None:
                    raise ValueError(f"Field '{field_name}' of type '{field_type}' requires 'bit'")
                mask = (1 << bits_count) - 1
                self.fields[field_name] = (
                    lambda frame_data,
                           byte_idx=adjusted_byte,
                           bit_val=bit,
                           mask_val=mask,
                           r_val=resolution,
                           off_val=offset: (
                        ((frame_data[byte_idx] >> bit_val) & mask_val) * r_val + off_val
                    )
                )
            elif field_type == "T":
                self.fields[field_name] = (
                    lambda frame_data,
                           byte_idx=adjusted_byte,
                           r_val=resolution,
                           off_val=offset: (
                        int.from_bytes(frame_data[byte_idx:byte_idx+3], byteorder=byte_order) * r_val + off_val
                    )
                )
            elif field_type in ("B", "H", "I") or (len(field_type) == 1):
                fmt = order_prefix + field_type
                try:
                    struct_obj = struct.Struct(fmt)
                except struct.error as e:
                    print(f"fmt: {fmt}, field_name: {field_name} frame_id: {frame_id} pgn: {pgn}")
                    raise e
                self.fields[field_name] = (
                    lambda frame_data,
                           byte_idx=adjusted_byte,
                           struct_object=struct_obj,
                           r_val=resolution,
                           off_val=offset: (
                        struct_object.unpack_from(frame_data, byte_idx)[0] * r_val + off_val
                    )
                )
            else:
                raise ValueError(f"Unsupported type '{field_type}' for field '{field_name}'")

    def deserialize(self, frame):
        if self.frame_id is not None:
            if frame.id != self.frame_id:
                return None
        elif self.pgn is not None:
            if not frame.is_extended:
                return None

            _, pgn_extracted = extract_j1939(frame.id)

            if pgn_extracted != self.pgn:
                return None
        else:
            raise ValueError("Identification parameters (id or pgn) must be provided")

        frame_data = frame.data

        result = {}
        for field_name, extractor in self.fields.items():
            try:
                result[field_name] = extractor(frame_data)
            except Exception as e:
                # print(e)
                return None
        return result


class CanDecoder(BaseNode):
    def __init__(self):
        super().__init__('can_decoder')
        self.can_deserializers = {}
        self.drill_publishers = {}
        self.can_subscribers = {}
        self.message2values_map = {}
        self.out_messages = {}
        self.message_updated_flags = {}
        self.values2msg_map = {}
        self.can_subscribers = {}
        package_name = "can_decoder"
        share_dir = get_package_share_directory(package_name)
        protocol_dir = os.path.join(share_dir, "protocols")

        protocols = {}
        for val_name, val_params in self.node_params['values'].items():
            if val_params['can_channel'] not in self.can_deserializers:
                self.can_deserializers[val_params['can_channel']] = {}

            protocol_name = val_params['protocol']
            if protocol_name not in protocols:
                protocol_file = os.path.join(protocol_dir, f"{protocol_name}.yaml")
                try:
                    with open(protocol_file, 'r') as f:
                        protocols[protocol_name] = yaml.safe_load(f)
                except FileNotFoundError:
                    self.log(f"Protocol file '{protocol_file}' not found", level=self.ERROR, event_code=self.events.SW_ERROR)
                    raise

            protocol = protocols[val_params['protocol']]
            message_id = protocol.get('pgn', protocol.get('id'))
            if message_id is None:
                raise ValueError("Identification parameters (id or pgn) must be provided")
            if message_id not in self.can_deserializers[val_params['can_channel']]:
                self.can_deserializers[val_params['can_channel']][message_id] = CANDeserializer(
                    frame_id=message_id if 'pgn' not in protocol else None,
                    pgn=message_id if 'pgn' in protocol else None,
                    data=protocol['data']
                )

            message_key = (message_id, val_params['source_id'] if 'source_id' in val_params else None)

            if message_key not in self.message2values_map:
                self.message2values_map[message_key] = {}
            self.message2values_map[message_key][val_params['value_field']] = val_name

        for out_topic_name, out_topic_params in self.node_params['output_topics'].items():
            package, msg_name = out_topic_params['msg_type'].split('/')
            module_name = f"{package}.msg"

            try:
                mod = importlib.import_module(module_name)
                msg_class = getattr(mod, msg_name)
            except (ModuleNotFoundError, AttributeError) as e:
                self.log(f"Message import error {out_topic_params['msg_type']}: {e}", level=self.ERROR)
                raise

            # check msg fields
            msg_inst = msg_class()
            for field_path in out_topic_params['fields']:
                current = msg_inst
                for attr in field_path.split('.'):
                    if not hasattr(current, attr):
                        raise AttributeError(
                            f"Field '{field_path}' is not defined in message {out_topic_params['msg_type']}"
                        )
                    current = getattr(current, attr)

            publisher = self.create_publisher(msg_class, out_topic_name, 10)
            self.drill_publishers[out_topic_name] = publisher
            self.out_messages[out_topic_name] = msg_class()
            self.message_updated_flags[out_topic_name] = {}
            for msg_field, val_name in out_topic_params['fields'].items():
                self.message_updated_flags[out_topic_name][msg_field] = False
                if val_name not in self.values2msg_map:
                    self.values2msg_map[val_name] = []
                self.values2msg_map[val_name].append((out_topic_name, msg_field))

        # test configuration
        dummy_data = bytes([0] * 8)
        for channel, des_map in self.can_deserializers.items():
            for msg_id, deserializer in des_map.items():
                for field_name, extractor in deserializer.fields.items():
                    try:
                        extractor(dummy_data)
                    except Exception as e:
                        raise RuntimeError(
                            f"Configuration error in CANDeserializer for channel='{channel}', "
                            f"PGN/ID={msg_id}, field='{field_name}': {e}"
                        ) from e

    def can_message_callback(self, msg, can_channel):
        # if msg.id > 0x7FF:
        #     msg.is_extended = True

        src, pgn = (extract_j1939(msg.id) if msg.is_extended else (None, None))

        channel_deserializers = self.can_deserializers.get(can_channel)
        if not channel_deserializers:
            # self.log(f"Drop message on channel {can_channel}. No deserializers found")
            return

        if msg.is_extended:
            deserializer = channel_deserializers.get(pgn) or channel_deserializers.get(msg.id)
            if not deserializer:
                # self.log(f"No deserializer found for pgn {pgn} or message id {msg.id}", level=self.ERROR)
                return
            val_map = (self.message2values_map.get((pgn, src)) or
                       self.message2values_map.get((pgn, None)) or
                       self.message2values_map.get((msg.id, None)))
        else:
            deserializer = channel_deserializers.get(msg.id)
            if not deserializer:
                # self.log(f"No deserializer found for message id {msg.id}", level=self.ERROR)
                return
            val_map = self.message2values_map.get((msg.id, None))

        if not val_map:
            self.log(
                f"Deserializer for message {msg.id} is defined, but value map is missing, check the code!",
                level=self.ERROR, period=1)
            return

        data = deserializer.deserialize(msg)
        # self.log(f"deserialized data: {data}")
        if data is None:
            self.log(f"Failed to deserialize message id={msg.id} with deserializer id {deserializer.frame_id}", level=self.ERROR)
            return

        affected_topics = set()
        for k, v in data.items():
            val_name = val_map.get(k)
            if val_name is None:
                continue

            for out_topic_name, msg_field in self.values2msg_map[val_name]:
                set_nested_attr(self.out_messages[out_topic_name], msg_field, v)
                self.message_updated_flags[out_topic_name][msg_field] = True
                affected_topics.add(out_topic_name)

        for out_topic_name in affected_topics:
            updated_in_full = True
            if hasattr(self.out_messages[out_topic_name], 'header'):
                self.out_messages[out_topic_name].header.stamp = self.get_rostime()
            for msg_field in self.node_params['output_topics'][out_topic_name]['fields'].keys():
                if not self.message_updated_flags[out_topic_name][msg_field]:
                    updated_in_full = False
                    break
            if updated_in_full:
                for msg_field in self.message_updated_flags[out_topic_name].keys():
                    self.message_updated_flags[out_topic_name][msg_field] = False
                self.drill_publishers[out_topic_name].publish(self.out_messages[out_topic_name])

    def initialize(self):
        for can_channel in self.can_deserializers.keys():
            self.can_subscribers[can_channel] = self.create_subscription(
                Frame,
                can_channel,
                lambda msg, channel=can_channel: self.can_message_callback(msg, channel),
                10
            )


def main():
    rclpy.init()
    node = CanDecoder()
    node.run()


if __name__ == '__main__':
    main()
