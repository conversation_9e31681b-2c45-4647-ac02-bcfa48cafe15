# CanDecoder

## 1. Назначение и общий контекст

**Задача ноды.** `CanDecoder` преобразует «сырые» CAN‑кадры (`can_msgs/msg/Frame`) в типизированные ROS 2‑сообщения, опираясь на YAML‑описания протоколов и структуру сообщений/полей, заданную в параметрах ноды.  

**Позиция в системе.** Нода располагается между драйвером CAN‑шины и высокоуровневыми прикладными компонентами:

- **Вход** — один или несколько топиков `/canX` с сообщениями `Frame`, публикуемыми, например, `nmttcan_interface`.
- **Выход** — произвольные ROS-2‑топики с сообщениями заданных типов (например, `sensor_msgs/Imu`, `std_msgs/Float32`, либо кастомные), используемые остальной частью системы.

**Выполняемые задачи:**

- Декодирование полей кадра (маскирование, учёт порядка байт, масштаб и смещение).
- Агрегация значений из разных кадров и публикация ROS‑сообщения **только после** того, как обновлены все необходимые поля.
- Проставление временной метки (`header.stamp`), если выходной тип содержит `header`.
- Логирование несоответствий конфигурации и ошибок десериализации.

---

## 2. Файлы конфигурации

### 2.1 YAML описание CAN протокола

Размещается в пакете `can_decoder` в директории `protocols`. Один файл описывает **один** CAN‑кадр с поддержкой PGN по стандарту J1939, или полный ID кадра. Структура файла протокола:

```yaml
# engine_speed.yaml — пример
pgn:    0xF004        # J1939 PGN (либо используйте id: 0x123 для классического CAN)
byte_order: little    # опционально; little — по умолчанию

data:
  ENG_SPEED:          # логическое имя поля
    type: H           # 16‑бит беззнаковый (uint16)
    byte: 1           # первый байт (1‑based)
    resolution: 1/8   # 0.125 rpm на единицу
    offset: 0

  ENABLE:             
    type: t           # одно из битовых полей
    byte: 3           # третий байт (1‑based)
    bit: 0            # младший бит третьего байта
```

**Поля верхнего уровня:**

| Ключ         | Значение                                                                      | Кому нужен                                                                                           |
| ------------ | ----------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------- |
| `id`         | Канальный ID (0 – 0x7FF) для стандартного CAN. **Взаимоисключающий** с `pgn`. | Десериализатор ищет совпадение по точному `frame_id`.                                                |
| `pgn`        | PGN (Parameter Group Number) J1939. Допустим hex/dec/0‑prefixed oct.          | Для расширенного кадра; совместно с `source_id` в параметрах ноды можно разделять множественные ECU. |
| `byte_order` | `little` (по умолчанию) или `big`.                                            | Определяет префикс `<` или `>` в `struct.unpack_from`.                                               |
| `data`       | Словарь описаний полей (см. ниже).                                            | Главная часть — именно она управляет извлечением данных.                                             |

#### Описание поля внутри `data`

| Ключ         | Тип/формат                                     | Обязательно                | Описание                                                                                                                  |
| ------------ | ---------------------------------------------- | -------------------------- | ------------------------------------------------------------------------------------------------------------------------- |
| `type`       | Строка (см. таблицу поддерживаемых типов ниже) | ✔                          | Определяет, как интерпретировать байты/биты.                                                                              |
| `byte`       | Целое ≥ 1                                      | ✔                          | **1‑based** индекс первого байта поля в кадре.                                                                            |
| `bit`        | 0‑7                                            | толькo для `t`, `ti`, `uN` | Бит внутри выбранного байта (0 — LSB).                                                                                    |
| `resolution` | Число или строка `"N/D"`                       | —                          | Множитель: итог = raw × resolution + offset. Строка поддерживает рациональные коэффициенты без округления, например, 1/8. |
| `offset`     | Число                                          | —                          | Смещение, добавляемое после масштабирования.                                                                              |
| любые др.    | люб.                                           | —                          | Игнорируются кодом, но полезны для комментариев (`units`, `comment`, и т. п.).                                            |

##### Поддерживаемые типы `type`

| Тип                          | Интерпретация                                                   | Требует `bit` | Примечания                                                      |
| ---------------------------- | --------------------------------------------------------------- | ------------- | --------------------------------------------------------------- |
| `t`                          | Логический, «истина» если указанный бит = 1                     | ✔             | Битовые флаги.                                                  |
| `ti`                         | Логический, «истина» если бит = 0 (инвертированный)             | ✔             | Инвертированные флаги.                                          |
| `uN`                         | Беззнаковое поле из **N** бит (1 ≤ N ≤ 8)                       | ✔             | Например `u3` извлекает 3‑битовое значение из указанного байта. |
| `T`                          | 24‑бит беззнаковое (`uint24`)                                   | —             | Читает **три** последовательных байта, учитывая `byte_order`.   |
| однобукв. формат из `struct` | Любой поддержанный `struct` символ (`b`, `h`, `i`, `f`, и т.д.) | —             | Применяется как `struct.Struct(order_prefix + type)`.           |

> **Важно:** для многобайтовых типов порядок байт контролируется атрибутом `byte_order` на уровне всего файла.

#### Полный минимальный пример (стандартный CAN‑ID)

```yaml
# coolant_temp.yaml
id: 0x123

data:
  COOLANT_TEMP:
    type: B
    byte: 3
    resolution: 1
    offset: -40
```

#### Пример с несколькими полями J1939

```yaml
# J1939 VEP1
# Vehicle Electrical Power 1.

pgn: 0xfef7  # 65271

data:
  # Battery Potential / Power Input 1.
  # Range: 0 to 3212.75 V
  battery_potential: {
    type: H, byte: 5, resolution: 0.05, offset: 0
  }

  # Keyswitch Battery Potential.
  # Range: 0 to 3212.75 V
  keyswitch_potential: {
    type: H, byte: 7, resolution: 0.05, offset: 0
  }
```

---

### 2.2 Параметры ноды

Получаются от сервера параметров (см. описание BaseNode) и определяют **сопоставление полей CAN‑кадров с публикуемыми ROS‑сообщениями**. Представлены разделами `values` и `output_topics`:

```yaml
can_decoder:
  values:
    <logical_value_name>:
      protocol:    <protocol_yaml_basename>   # без .yaml
      can_channel: <can_frame_topic>          # например, /can0
      value_field: <field_name_in_protocol>   # ключ из data: в описании кадра
      source_id:   0xXX                       # (опц.) J1939 Source Address

  output_topics:
    <topic_name>:
      msg_type: <package/MsgType>             # пример: geometry_msgs/Vector3
      fields:
        <msg_field_path>: <logical_value_name>
        # поддерживается точечная нотация для вложенных полей
```

#### Блок `values`

| Поле          | Тип            | Обязательно | Значение по коду                                                                  |
| ------------- | -------------- | ----------- | --------------------------------------------------------------------------------- |
| `protocol`    | string         | ✔           | Имя YAML‑файла в `protocols/` без расширения; по нему находится десериализатор.   |
| `can_channel` | string (topic) | ✔           | Имя топика с `can_msgs/Frame`, на который нужно подписаться.                      |
| `value_field` | string         | ✔           | Ключ из секции `data:` выбранного протокола; указывает, какое поле брать.         |
| `source_id`   | int (hex/dec)  | —           | Фильтр Source Address для J1939. Если задан, значения от других ECU игнорируются. |

> **Имя логического значения** (`logical_value_name`) должно быть уникальным: оно служит ключом для обмена между блоками `values` и `output_topics`.

#### Блок `output_topics`

| Поле       | Тип    | Обязательно | Описание                                                                            |
| ---------- | ------ | ----------- | ----------------------------------------------------------------------------------- |
| `msg_type` | string | ✔           | Имя импортируемого ROS2 сообщения: `package/Msg`. Загружается через `importlib`.    |
| `fields`   | dict   | ✔           | Сопоставление *пути к полю* в сообщении (ключ) ⇢ *логического значения* (значение). |

**Путь к полю** может быть вложенным (`orientation.x`, `pose.position.z`). Функция `set_nested_attr` рекурсивно проходит по объекту сообщения и присваивает значение.

Нода публикует сообщение **только тогда**, когда для данного топика обновлены **все** перечисленные в `fields` поля.

#### Минимальный рабочий пример

```yaml
can_decoder:
  values:
    engine_speed:
      protocol: engine_speed      # protocols/engine_speed.yaml
      can_channel: /can0
      value_field: ENG_SPEED

  output_topics:
    /engine/rpm:
      msg_type: std_msgs/Float32
      fields:
        data: engine_speed        # записываем RPM в std_msgs/Float32.data
```

#### Комплексный пример (многоканальная конфигурация с вложенными путями)

```yaml
can_decoder:
  values:
    # --- IMU: ориентация (углы Эйлера) ------------------
    roll:
      protocol: imu_attitude
      can_channel: /can0
      value_field: ROLL

    pitch:
      protocol: imu_attitude
      can_channel: /can0
      value_field: PITCH

    yaw:
      protocol: imu_attitude
      can_channel: /can0
      value_field: YAW

    # --- IMU: линейное ускорение ------------------------
    acc_x:
      protocol: imu_accel
      can_channel: /can0
      value_field: ACC_X
    acc_y:
      protocol: imu_accel
      can_channel: /can0
      value_field: ACC_Y
    acc_z:
      protocol: imu_accel
      can_channel: /can0
      value_field: ACC_Z

    # --- Двигатель --------------------------------------
    engine_speed:
      protocol: engine_speed
      can_channel: /can1
      value_field: ENG_SPEED

    coolant_temp:
      protocol: coolant_temp
      can_channel: /can1
      value_field: COOLANT_TEMP
      source_id: 0x05   # фильтруем пакеты конкретного ECU

  output_topics:
    /imu/euler:
      msg_type: geometry_msgs/Vector3
      fields:
        x: roll          
        y: pitch
        z: yaw

    /imu/accel:
      msg_type: geometry_msgs/Vector3
      fields:
        x: acc_x
        y: acc_y
        z: acc_z

    /engine/status:
      msg_type: custom_msgs/EngineStatus
      fields:
        temperature.coolant: coolant_temp
        speed.rpm:          engine_speed

```

---

## 3. Алгоритм десериализации и публикации

> Далее описан жизненный цикл кадра CAN от приёма во входном топике до публикации ROS‑сообщения.

### 3.1 Подписка на CAN‑каналы

- В `initialize()` нода перебирает ключи словаря `can_deserializers` (заполняется при старте из блока `values`).
- Для каждого уникального `can_channel` создаётся подписка `create_subscription(Frame, can_channel, callback, 10)`.
- Лямбда фиксирует имя канала в скрытом параметре `channel`, чтобы внутри `can_message_callback` знать, к какому набору десериализаторов обращаться.

### 3.2 Разбор идентификатора кадра

```python
src, pgn = extract_j1939(msg.id) if msg.is_extended else (None, None)
```

`extract_j1939()` реализует правило J1939:

| Поле   | Биты кадра | Описание       |
| ------ | ---------- | -------------- |
| **DP** | 24         | Data Page      |
| **PF** | 23‑16      | PDU Format     |
| **PS** | 15‑8       | PDU Specific   |
| **SA** | 7‑0        | Source Address |

- Если **PF < 240** (PDU‑1, адрес‑направленный кадр) → PGN = `DP<<16 | PF<<8` (PS зануляется).
- Если **PF ≥ 240** (PDU‑2, широковещательный кадр) → PGN = `DP<<16 | PF<<8 | PS` — PS входит в **PGN**.

Для **стандартного CAN‑кадра** (`is_extended == False`) вычисления PGN не проводится; дальнейшая обработка опирается на полный `msg.id`.

### 3.3 Выбор десериализатора

Далее выбор десериализатора производится следующим образом:

```text
if msg.is_extended:
    deserializer = D[channel].get(pgn) or D[channel].get(msg.id)
else:
    deserializer = D[channel].get(msg.id)
```

- `D` — словарь `can_deserializers[can_channel]`.
- Для расширенного кадра сначала пробуем PGN (универсальное описание), затем  полный ID.
- Для стандартного кадра поиск выполняется по ID сообщения
- При отсутствии десериализатора кадр пропускается без логирования.

### 3.4 Карта значений (`message2values_map`)

**Что это такое?**  
Карта значений — это промежуточная структура вида

```python
message2values_map: {
    (message_id, source_id|None): {<can_field_name>: <logical_value_name>, ...},
    ...
}
```

Она связывает *имя поля в CAN-кадре* (как оно прописано в YAML‑протоколе) с *логическим именем значения* (ключ из блока `values` в `node_params`). Благодаря этому нода знает, **какое именно ROS‑поле следует обновить, получив конкретное поле CAN кадра**.

#### Поиск карты при обработке кадра

В `can_message_callback` порядок поиска зависит от типа кадра:

```python
if msg.is_extended:
    val_map = (self.message2values_map.get((pgn, src)) or
               self.message2values_map.get((pgn, None)) or
               self.message2values_map.get((msg.id, None)))
else:
    val_map = self.message2values_map.get((msg.id, None))
```

1. **Полное совпадение «PGN + Source ID»** — нацеленное на конкретный ECU.
2. **PGN без учёта Source ID** — общая схема (например, если Source ID не задан).
3. **Полный CAN‑ID** — применяется для стандартного (не расширенного) кадра и случаев, когда для расширенного кадра указан полный ID.

### 3.5 Десериализация кадра

```python
data = deserializer.deserialize(msg)
```

- `CANDeserializer.deserialize` проверяет соответствие `frame_id`/`pgn`, затем перебирает заранее сгенерированные лямбды‑экстракторы для заданных полей кадра.
- Для каждого поля выполняются шаги:
  1. Вычисление «сырых» битов/байтов (`int.from_bytes`, маскирование).
  2. Приведение типа (`int`, `float`, `bool`).
  3. Применение `resolution` и `offset`.
- При любой ошибке (выход за пределы длины кадра, `struct.error`, деление на ноль и т. д.) метод возвращает `None`, а кадр игнорируется.

### 3.6 Запись значений в выходные сообщения

Для каждого `<field_name>: value` из `data`:

1. Находим логическое имя: `val_name = val_map.get(field_name)`.
2. Для всех топиков/полей, требующих это значение (`values2msg_map[val_name]`) вызывается:

   ```python
   set_nested_attr(out_msg, msg_field_path, value)
   message_updated_flags[topic][msg_field] = True
   ```
3. Собираем множество `affected_topics` для последующего прохода.

### 3.7 Условие публикации

После обработки всех полей кадра нода итерируется по `affected_topics`:

```python
updated_in_full = all(message_updated_flags[topic][f] for f in required_fields)
```

- `required_fields` — список ключей из `output_topics/<topic>/fields`.
- Только если **все** требуемые поля внесены, сообщение считается «собранным».
- Если сообщение содержит `header`, поле `stamp` устанавливается текущим временем `self.get_rostime()`.
- Сообщение публикуется паблишером `drill_publishers[topic].publish(msg)`.
- Флаги `message_updated_flags[topic][*]` сбрасываются в `False` для нового цикла.

## 4. Выводимые сообщения об ошибках

| Где возникает                            | Условие                                                    | Текст сообщения / исключения                                                              | Уровень / тип             |
| ---------------------------------------- | ---------------------------------------------------------- | ----------------------------------------------------------------------------------------- | ------------------------- |
| Загрузка протоколов                      | Не найден YAML‑файл протокола                              | `Protocol file '<path>' not found`                                                        | `self.ERROR` (`SW_ERROR`) |
| Импорт сообщений                         | Невозможно импортировать `package/MsgType`                 | `Message import error <package/MsgType>: <exception>`                                     | `self.ERROR`              |
| Обработка кадра                          | Есть десериализатор, но нет карты значений                 | `Deserializer for message <id> is defined, but value map is missing, check the code!`     | `self.ERROR` (periodic)   |
| Десериализация кадра                     | `deserialize()` вернул `None` (ошибка формата/длины и др.) | `Failed to deserialize message id=<id> with deserializer id <frame_id>`                   | `self.ERROR`              |
| Валидация конфигурации `CANDeserializer` | Не задан параметр `data`                                   | `ValueError: Data dictionary with field definitions is required`                          | Исключение                |
| Валидация описания поля                  | Нет ключа `byte`                                           | `ValueError: Field '<name>' must include 'byte' parameter`                                | Исключение                |
| Валидация битового поля                  | Нет `bit` для типов `t`, `ti`, `uN`                        | `ValueError: Field '<name>' of type '<type>' requires 'bit'`                              | Исключение                |
| Неподдерживаемый тип поля                | `type` не распознан                                        | `ValueError: Unsupported type '<type>' for field '<name>'`                                | Исключение                |
| Ошибка идентификации кадра               | Не передан `frame_id` и `pgn`                              | `ValueError: Identification parameters (frame_id or pgn) must be provided`                | Исключение                |
| Ошибка формата `struct`                  | Некорректный формат (`struct.Struct` возбуждает `error`)   | `fmt: <fmt>, field_name: <name> frame_id: <id> pgn: <pgn>` (debug‑print) + `struct.error` | stderr + исключение       |
