# Документация базового класса ROS2 ноды (BaseNode)

## 1. Описание класса

**BaseNode** – базовый класс для создания ROS2 нод, наследуемых в рамках проекта. Он расширяет функциональность стандартного класса `rclpy.node.Node` и предоставляет следующие возможности:

- **Инициализация ноды и корректное завершение работы:**  
  Автоматическая установка обработчика SIGINT, отмена таймеров, публикация финального отчёта и вызов `rclpy.shutdown()`.

- **Публикация отчётов и событий:**  
  Встроенные публикаторы для сообщений типа `Report` и `Event` позволяют информировать систему о текущем статусе ноды и возникших событиях.

- **Работа с параметрами:**  
  Механизм запроса параметров с сервера (адрес и порт которого задаются через переменные окружения) для различных контекстов (имя ноды, "Vehicle", "Global"). Обновление параметров происходит по уведомлениям через подписку на `ParamNotification`.

- **Организация основного цикла работы:**  
  Методы `initialize()` и `do_work()` служат для реализации логики ноды, а таймеры обеспечивают регулярное выполнение задач.

- **Расширенная система логирования:**  
  Метод `log()` реализует логирование с поддержкой публикации событий и подавления повторяющихся сообщений. Механизм основан на месте вызова (файл и номер строки), что позволяет использовать динамические строки (например, содержащие числовые данные).

## 2. Основные компоненты класса

### Константы уровня логирования

На уровне класса определены следующие константы, использующие значения из сообщения `Event`:
- `DEBUG`
- `INFO`
- `WARN`
- `ERROR`
- `FATAL`

Эти уровни используются для классификации важности логируемых сообщений.

### Публикация отчётов и событий

- **Публикация отчёта (`Report`):**  
  Метод `publish_report()` создаёт сообщение с текущим статусом ноды (например, `STATUS_INIT`, `STATUS_WORKING`, `STATUS_FAILURE`, `STATUS_EXITING`) и публикует его через `_report_pub`.

- **Публикация событий (`Event`):**  
  Метод `log()` не только выводит сообщение через стандартный логгер ROS2, но и, при наличии заданного `event_code`, публикует сообщение типа `Event` с информацией о событии. Доступные коды событий импортируются в базовый класс и могут быть получены как, например, `self.events.SW_ERROR`.

### Работа с параметрами

- **Конфигурация через переменные окружения:**  
  Значения, такие как частота публикации отчётов (`REPORT_RATE`), адрес сервера параметров (`PARAM_SERVER_ADDRESS`) и порт сервера (`PARAM_SERVER_PORT`), задаются через переменные окружения. Если переменная не установлена, используются значения по умолчанию (например, 1.0 для `REPORT_RATE`).

- **Запрос параметров:**  
  Метод `_query_params(key: str)` формирует URL с использованием переменных окружения и отправляет HTTP GET запрос для получения параметров по указанному ключу.

- **Обработка обновлений параметров:**  
  Подписка на тему `/param_update_notification` позволяет автоматически запрашивать обновлённые параметры. Если изменения касаются интересующих ключей (имя ноды, "Vehicle" или "Global"), вызывается метод `_update_params_and_notify()`, который обновляет локальные словари параметров и вызывает `on_params_update()` для реализации специфической логики в наследнике.

### Организация рабочего цикла

- **Метод `initialize()`:**  
  Абстрактный метод для инициализации ресурсов ноды. Должен быть переопределён в наследуемых классах.

- **Метод `do_work()`:**  
  Основная функция, содержащая логику работы ноды. Также обязателен для переопределения.

- **Метод `work_callback()`:**  
  Вызывается периодически через таймер и оборачивает вызов `do_work()` в блок `try/except`. При возникновении исключения статус ноды устанавливается в `STATUS_FAILURE`, а при последующем успешном выполнении — возвращается в `STATUS_WORKING`.

### Жизненный цикл ноды

- **Метод `start()`:**  
  Вызывает `initialize()`, устанавливает статус ноды, создаёт таймер для `work_callback()` и вызывает `rclpy.spin(self)` для поддержания работы ноды.

- **Метод `stop()`:**  
  Отменяет все таймеры, публикует финальный отчёт с установкой статуса `STATUS_EXITING`, уничтожает ноду и завершает работу через `rclpy.shutdown()`.

- **Метод `run()`:**  
  Обеспечивает запуск ноды и корректное завершение при перехвате `KeyboardInterrupt`.

### Расширенная система логирования

Метод `log(message: str, level=INFO, event_code=None, require_remote=False, period=0.0)` реализует следующие возможности:

- **Вывод сообщения:**  
  Сообщение выводится через соответствующий метод логгера ROS2 (debug, info, warn, error) в зависимости от уровня.

- **Публикация события:**  
  Если задан `event_code`, дополнительно публикуется сообщение типа `Event` с информацией о событии.

- **Подавление повторных сообщений:**  
  Аргумент `period` позволяет ограничить частоту логирования – если повторный вызов происходит в течение указанного периода, сообщение не логируется и событие не публикуется. Поскольку механизм ориентируется на место вызова (файл и номер строки) динамические данные, вставляемые в текст сообщения (например, числовые значения), не влияют на подавление.

- **Указание необходимости ДУ:**  
  Аргумент `require_remote` управляет соответствующим полем в сообщении `Event`. Событие с установленным флагом `require_remote` инициирует запрос дистанционного управления.

### Методы получения времени

В классе **BaseNode** реализованы два метода для получения текущего времени, аналогичные подходу, применяемому в ROS1:

- **get_time()**  
  Этот метод возвращает текущее время в виде числа с плавающей запятой, представляющего количество секунд. Аналогично функции `rospy.get_time()` в ROS1.

- **get_rostime()**  
  Метод возвращает текущее время в виде объекта сообщения времени ROS2 (типа `builtin_interfaces.msg.Time`). Это значение может быть использовано для заполнения полей заголовков сообщений, аналогично функции `rospy.get_rostime()` в ROS1. 

## 3. Правила использования

При создании новой ноды, наследуемой от **BaseNode**, рекомендуется:

- **Переопределять методы:**
  - `initialize()` — для инициализации ресурсов (создание подписок, сервисов и т.д.).
  - `do_work()` — для реализации основной логики работы ноды.
  - `on_params_update()` (опционально) — для обработки обновлений параметров при изменении конфигурации.

- **Запуск ноды:**  
  Используйте метод `run()` для запуска ноды, который обеспечивает корректное завершение работы при возникновении исключений или сигнале прерывания.

- **Логирование:**  
  Применяйте метод `log()` для вывода сообщений с заданными уровнями логирования и публикацией события при необходимости. 

- **Конфигурация через переменные окружения:**  
  Задавайте значения, такие как адрес параметрического сервера, порт и частоту публикации отчётов, через переменные окружения. Это позволяет изменять конфигурацию без изменения исходного кода.

## 4. Пример XML launch файла

Ниже приведён пример фрагмента XML launch файла, в котором задаются переменные окружения для ноды:
```xml
<set_env name="PARAM_SERVER_ADDRESS" value="localhost" />
<set_env name="PARAM_SERVER_PORT" value="5000" />
<set_env name="PARAM_SERVER_LOGLEVEL" value="info" />
<set_env name="PARAM_DUMP_FILE" value="~/.drill_params_dump.json" />
<set_env name="REPORT_RATE" value="5.0" />

<include file="$(find-pkg-share params_server)/launch/normal.xml"/>
<node pkg="can_decoder"
      exec="can_decoder"
      name="can_decoder"
      output="screen"
      respawn="true"
      respawn_delay="5" />
```

## 5. Пример наследующей ноды

Ниже приведён пример создания ноды, наследуемой от **BaseNode**:

```python
import rclpy
from base_node import BaseNode  # Импорт базового класса

class MyCustomNode(BaseNode):
    def __init__(self):
        super().__init__('my_custom_node')
        
    def initialize(self):
        # Инициализация ноды: создание дополнительных подписок, сервисов и т.д.
        self.log("Инициализация MyCustomNode...", level=self.INFO)
        # Дополнительная логика и установка ресурсов

    def do_work(self):
        # Основной рабочий цикл
        self.log(f"Выполнение основной работы. {self.get_time()}", level=self.DEBUG)

    def on_params_update(self):
        # Обработка обновления параметров
        self.log("Параметры обновлены. Применение новой конфигурации...", level=self.INFO)
        # Реализуйте дополнительную логику при изменении параметров

def main():
    rclpy.init()
    node = MyCustomNode()
    node.run()

if __name__ == '__main__':
    main()
