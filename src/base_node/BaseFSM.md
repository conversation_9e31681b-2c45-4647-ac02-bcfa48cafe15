# Документация по использованию класса BaseFSM

## Содержание
1. [Введение](#введение)
2. [Возможности BaseFSM](#возможности-basefsm)
3. [Архитектура FSM](#архитектура-fsm)
4. [Жизненный цикл состояния](#жизненный-цикл-состояния)
5. [Создание собственного FSM](#создание-собственного-fsm)
6. [Работа с состояниями](#работа-с-состояниями)
7. [Обязательные методы для переопределения](#обязательные-методы-для-переопределения)
8. [Опциональные методы](#опциональные-методы)
9. [Управление подписчиками](#управление-подписчиками)
10. [Обработка ошибок](#обработка-ошибок)
11. [Пример полной реализации](#пример-полной-реализации)
12. [Дополнительные возможности](#дополнительные-возможности)

## Введение
FSM (конечный автомат) — это модель поведения системы, в которой в каждый момент времени система находится в одном из конечного числа состояний. Переходы между состояниями происходят в ответ на события или условия.

Класс `BaseFSM` представляет собой основу для создания конечного автомата  на ROS2. Он упрощает управление состояниями системы, обеспечивает переходы между состояниями, публикует статус FSM и обрабатывает внешние команды и события безопасности.

## Возможности BaseFSM

- **Управление набором состояний**: регистрация, переключение между состояниями, хранение предыдущего состояния
- **Встроенное состояние failure**: автоматический переход при ошибках с безопасной остановкой
- **Создание подписчиков**: с указанием таймаута и удобным доступом к полученным данным (не требуется вручную регистрировать коллбэк)
- **Автоматические проверки безопасности**: обработка топиков `/permission`, `/robomode` и таймаутов подписчиков
- **Гибкие настройки состояний**: игнорирование определенных проверок для каждого состояния
- **Расширенная система логирования**: с автоматическим переходом в failure при ошибках, дополняет функционал, предоставляемый BaseNode, в т.ч. запрос ДУ и публикацию event-а.
- **Публикация статуса**: автоматическая публикация текущего состояния в ROS-топик
- **Обработка внешних команд**: возможность изменения состояния через ROS-сообщения
- **Отслеживание времени состояния**: учёт "чистого" времени работы состояния

## Архитектура FSM

`BaseFSM` построен как расширение базового класса `BaseNode` и состоит из следующих ключевых компонентов:

- **Набор состояний**: словарь состояний, наследующихся от `BaseState`
- **Текущее состояние**: активное состояние, выполняющее свою работу в цикле
- **Предыдущее состояние**: сохраняется для возможности возврата через команду принудительного перехода
- **Подписчики**: система отслеживания актуальности данных
- **Проверки безопасности**: система предотвращения опасных действий

Жизненный цикл работы FSM:
1. Инициализация с добавлением состояний через `add_states()`
2. Установка начального состояния через `set_state()`
3. Периодическое выполнение `do_work()` текущего состояния
4. Обработка переходов между состояниями по мере необходимости
5. Автоматический переход в `failure` при ошибках

## Жизненный цикл состояния

Каждое состояние проходит через следующие фазы:

1. **Конструирование**: настройка параметров состояния при создании
2. **Переход в состояние**: вызов `on_transition_to()` при активации состояния
3. **Выполнение работы**: периодический вызов `do_work()` текущего состояния
4. **Переход из состояния**: вызов `on_transition_from()` при выходе из состояния

Пример базового состояния:

```python
class IdleState(BaseState):
    def __init__(self, node: BaseFSM):
        super().__init__(
            name="idle",
            node=node,
            remember_as_prev=True,
            # Другие параметры конфигурации
        )
        
    def on_transition_to(self):
        self.log("Переход в состояние Idle")
        # Инициализация состояния
        
    def on_transition_from(self):
        self.log("Выход из состояния Idle")
        # Очистка ресурсов
        
    def do_work(self):
        # Периодическая работа
        self.log("Выполнение работы в состоянии Idle", period=5.0)
```

## Создание собственного FSM

Для создания собственного FSM, необходимо создать класс, наследующийся от `BaseFSM`, и переопределить обязательные методы:

```python
from base_node.base_fsm import BaseFSM, BaseState

class MyState1(BaseState):
    # Реализация состояния
    ...

class MyState2(BaseState):
    # Реализация другого состояния
    ...

class MyFSM(BaseFSM):
    def __init__(self):
        super().__init__(node_name="my_fsm")
        
        # Создание и добавление состояний
        self.add_states(
            MyState1(self),
            MyState2(self)
        )
        
        # Регистрация подписчиков
        self.add_subscriber(
            "/sensor_data", 
            SensorMsg, 
            "sensor", 
            timeout=1.0
        )
        
        # Установка начального состояния
        self.set_state("my_state1")
    
    def stop_control(self):
        # Реализация безопасной остановки
        self.log("Остановка всех приводов")
        # ...
    
    def safety_check(self) -> bool:
        # Реализация проверки безопасности
        return self.subs.sensor.value < 100.0
        
    # Другие необходимые переопределения...

# Использование:
def main(args=None):
    rclpy.init(args=args)
    fsm = MyFSM()
    fsm.run()

if __name__ == '__main__':
    main()
```

## Работа с состояниями

### Аргументы конструктора BaseState

При создании состояния через класс `BaseState` доступны следующие параметры:

| Параметр | Тип | Описание |
|----------|-----|----------|
| `name` | `str` | Уникальное имя состояния (обязательный) |
| `node` | `BaseFSM` | Ссылка на узел FSM (обязательный) |
| `remember_as_prev` | `bool` | Сохранять ли состояние как предыдущее (True по умолчанию) |
| `ignore_permission` | `bool` | Игнорировать ли проверку разрешения |
| `ignore_robomode` | `bool` | Игнорировать ли проверку режима робота |
| `ignore_outdated_subs` | `bool` | Игнорировать ли устаревшие данные подписчиков |
| `ignore_safety_check` | `bool` | Игнорировать ли проверку безопасности |
| `suppress_failure` | `bool` | Подавлять ли автоматический переход в failure при ошибках |

```python
class CalibrationState(BaseState):
    def __init__(self, node: BaseFSM):
        super().__init__(
            name="calibration",
            node=node,
            remember_as_prev=False,  # Не сохранять как предыдущее
            ignore_permission=True,  # Не требовать разрешения
            ignore_safety_check=True  # Игнорировать проверки безопасности
        )
```

### Свойства BaseState

Внутри состояния доступны следующие свойства:

| Свойство | Описание                                          |
|----------|---------------------------------------------------|
| `name` | Имя состояния                                     |
| `node` | Ссылка на узел FSM                                |
| `transition_kwargs` | Аргументы, переданные в `set_state()`             |
| `subs` | Доступ к подпискам (например, `self.subs.sensor`) |
| `node_params` | Параметры этой ROS ноды                           |
| `vehicle_params` | Параметры транспортного средства                  |
| `global_params` | Глобальные параметры                              |

Пример использования:

```python
def on_transition_to(self):
    self.speed = self.transition_kwargs.get("speed", 10.0)
    self.max_torque = self.node_params.get("max_torque", 100.0)
    self.log(f"Запуск с скоростью {self.speed}, ограничение момента: {self.max_torque}")
```

### Переключение между состояниями

Для смены состояния из любого метода FSM или состояния используйте метод `set_state()`:

```python
# Простой переход
self.node.set_state("running")

# Переход с параметрами
self.node.set_state("moving", speed=5.0, destination="home")

# Возврат к предыдущему состоянию
self.node.set_state("prev")
```

## Обязательные методы для переопределения

В вашем классе, наследующемся от `BaseFSM`, необходимо реализовать следующие методы:

### 1. stop_control()

Этот метод должен безопасно остановить все исполнительные механизмы. Вызывается автоматически:
- при переходе в состояние `failure`
- когда FSM не может выполнить регулярную работу
- во всех случаях, когда проверки безопасности не проходят

```python
def stop_control(self):
    self.log("Безопасная остановка системы")
    self.motor_controller.set_velocity(0.0)
    self.pump.disable()
    self.publish_zero_cmd_vel()
```

### 2. safety_check() -> bool

Этот метод должен выполнять проверки безопасности и возвращать `True`, если безопасно выполнять работу текущего состояния.

```python
def safety_check(self) -> bool:
    # Проверка показаний датчиков
    if self.subs.temperature is None or self.subs.temperature.value > 80.0:
        return False
        
    # Проверка границ рабочей зоны
    if self.subs.position is not None:
        if not self.is_position_safe(self.subs.position):
            return False
            
    return True
```

## Опциональные методы

Следующие методы можно переопределить для расширения функциональности:

### 1. on_subscribers_timeout(expired: List[str])

Вызывается при таймауте подписчиков. Полезно для диагностики и отладки.

```python
def on_subscribers_timeout(self, expired: List[str]):
    if "lidar" in expired:
        self.handle_error(
            "Потеряна связь с лидаром!",
            level=self.ERROR,
            event_code=self.events.SENSOR_FAILURE
        )
    elif "camera" in expired:
        self.log(
            "Нет данных с камеры более 1 секунды",
            level=self.WARN
        )
```

### 2. do_work_finally()

Вызывается в конце каждого цикла выполнения работы, независимо от результата.

```python
def do_work_finally(self):
    # Обновление индикаторов
    self.update_dashboard()
    
    # Сбор диагностики
    self.collect_metrics()
```

### 3. do_work_prevented()

Вызывается, когда регулярная работа текущего состояния не может быть выполнена.

```python
def do_work_prevented(self):
    # Дополнительные действия при блокировке работы
    self.log("Работа приостановлена", level=self.WARN)
    self.set_warning_lights(True)
```

## Управление подписчиками

### Регистрация подписчиков

FSM предоставляет механизм для регистрации подписчиков с автоматическим отслеживанием таймаутов:

```python
def __init__(self):
    super().__init__(node_name="my_fsm")
    
    # Стандартный подписчик
    self.add_subscriber(
        "/sensor/temperature",  # Топик
        TemperatureMsg,         # Тип сообщения
        "temperature",          # Имя для доступа через self.subs
        timeout=1.0             # Таймаут в секундах
    )
    
    # С настройкой QoS
    qos_profile = QoSProfile(
        depth=1,
        durability=DurabilityPolicy.TRANSIENT_LOCAL,
        reliability=ReliabilityPolicy.RELIABLE
    )
    
    self.add_subscriber(
        "/sensor/battery",
        BatteryStateMsg,
        "battery",
        timeout=5.0,
        qos_profile=qos_profile
    )
```

### Доступ к данным подписчиков

После регистрации подписчика его последнее сообщение доступно через `self.subs` (как в состоянии, так и в самой FSM ноде):

```python
def do_work(self):
    battery_level = self.subs.battery.percentage
    if battery_level < 10.0:
        self.node.set_state("low_battery")
```

## Обработка ошибок

FSM предоставляет систему обработки ошибок с автоматическим переходом в состояние `failure` при уровне выше WARNING.

### Использование handle_error()

```python
# Ошибка с переходом в failure
self.handle_error(
    "Критическая ошибка датчика!",
    level=self.ERROR,
    event_code=self.events.SENSOR_FAILURE
)

# Предупреждение без перехода в failure
self.handle_error(
    "Низкий заряд батареи",
    level=self.WARN,
    event_code=self.events.BATTERY_LOW
)

# Подавление повторных сообщений с одного места вызова
self.handle_error(
    "Потеряна связь с контроллером",
    level=self.ERROR,
    event_code=self.events.CONTROLLER_FAILURE,
    period=5.0  # Сообщение повторится не чаще чем раз в 5 секунд
)
```

### Использование log()

Для логирования без перехода в состояние `failure`:

```python
# Уровни логирования
self.log("Отладочная информация", level=self.DEBUG)
self.log("Информационное сообщение", level=self.INFO)
self.log("Предупреждение", level=self.WARN)
self.log("Ошибка", level=self.ERROR)
self.log("Критическая ошибка", level=self.FATAL)

# С подавлением повторов
self.log("Много повторяющихся сообщений", period=1.0)

# С кодом события для внешних систем
self.log(
    "Событие для внешних систем",
    event_code=self.events.CUSTOM_EVENT,
    require_remote=True
)
```
Методы `self.log` и `self.handle_error` доступны как в состоянии, так и в самой FSM ноде.
## Пример полной реализации

Ниже приведен пример полной реализации простого FSM для управления перемещением робота:

```python
import rclpy
from rclpy.qos import QoSProfile
from base_node.base_fsm import BaseFSM, BaseState
from geometry_msgs.msg import Twist
from sensor_msgs.msg import LaserScan
from drill_msgs.msg import RobotStatus

class IdleState(BaseState):
    def __init__(self, node: "RobotFSM"):
        super().__init__(
            name="idle",
            node=node,
            remember_as_prev=True
        )
    
    def on_transition_to(self):
        self.log("Переход в режим ожидания")
        self.node.stop_control()
    
    def do_work(self):
        # Ничего не делаем в режиме ожидания
        pass

class MoveForwardState(BaseState):
    def __init__(self, node: "RobotFSM"):
        super().__init__(
            name="move_forward",
            node=node,
            remember_as_prev=True
        )
        self.speed = 0.0
    
    def on_transition_to(self):
        self.log("Начало движения вперед")
        self.speed = self.transition_kwargs.get("speed", 0.5)
        self.log(f"Установлена скорость: {self.speed} м/с")
    
    def do_work(self):
        # Проверка на препятствия
        if self.subs.scan is not None:
            min_distance = min(self.subs.scan.ranges)
            if min_distance < 0.5:
                self.log(f"Обнаружено препятствие на расстоянии {min_distance} м")
                self.node.set_state("idle")
                return
        
        # Публикация команды движения
        cmd = Twist()
        cmd.linear.x = self.speed
        self.node.cmd_vel_pub.publish(cmd)

class RotateState(BaseState):
    def __init__(self, node: "RobotFSM"):
        super().__init__(
            name="rotate",
            node=node,
            remember_as_prev=True
        )
        self.angular_speed = 0.0
        self.target_angle = 0.0
        self.start_angle = 0.0
        self.current_angle = 0.0
    
    def on_transition_to(self):
        self.log("Начало поворота")
        self.angular_speed = self.transition_kwargs.get("speed", 0.5)
        self.target_angle = self.transition_kwargs.get("angle", 90.0)
        
        if self.subs.status is not None:
            self.start_angle = self.subs.status.orientation
        else:
            self.start_angle = 0.0
            self.log("Не удалось получить текущую ориентацию", level=self.WARN)
            
        self.current_angle = 0.0
        self.log(f"Поворот на {self.target_angle} градусов со скоростью {self.angular_speed} рад/с")
    
    def do_work(self):
        if self.subs.status is None:
            self.log("Нет данных о состоянии робота", level=self.WARN)
            return
            
        current_orientation = self.subs.status.orientation
        self.current_angle = abs(current_orientation - self.start_angle)
        
        # Проверка завершения поворота
        if self.current_angle >= abs(self.target_angle):
            self.log(f"Поворот завершен, достигнут угол {self.current_angle}")
            self.node.set_state("idle")
            return
            
        # Публикация команды поворота
        cmd = Twist()
        cmd.angular.z = self.angular_speed if self.target_angle > 0 else -self.angular_speed
        self.node.cmd_vel_pub.publish(cmd)


class RobotFSM(BaseFSM):
    def __init__(self):
        super().__init__(node_name="robot_fsm")
        
        # Создание издателя команд скорости
        self.cmd_vel_pub = self.create_publisher(
            Twist, 
            "/cmd_vel", 
            QoSProfile(depth=10)
        )
        
        # Регистрация подписчиков
        self.add_subscriber(
            "/scan", 
            LaserScan, 
            "scan", 
            timeout=0.5
        )
        
        self.add_subscriber(
            "/robot_status", 
            RobotStatus, 
            "status", 
            timeout=1.0
        )
        
        # Добавление состояний
        self.add_states(
            IdleState(self),
            MoveForwardState(self),
            RotateState(self)
        )
        
        # Установка начального состояния
        self.set_state("idle")
        
        self.log("RobotFSM инициализирован и готов к работе")
    
    def stop_control(self):
        """Безопасная остановка робота"""
        stop_cmd = Twist()  # Пустое сообщение = нулевые скорости
        self.cmd_vel_pub.publish(stop_cmd)
        self.log("Робот остановлен")
    
    def safety_check(self) -> bool:
        """Проверка безопасности работы"""
        # Проверка валидности данных сканера
        if self.subs.scan is not None:
            # Проверка наличия минимального расстояния до препятствий
            if min(self.subs.scan.ranges) < 0.2:  # Меньше 20 см
                self.log("Критически малое расстояние до препятствия!", level=self.WARN)
                return False
                
        # Проверка состояния робота
        if self.subs.status is not None:
            if self.subs.status.error_code != 0:
                self.log(f"Ошибка робота: {self.subs.status.error_message}", level=self.WARN)
                return False
                
            if self.subs.status.battery_level < 10.0:
                self.log("Критически низкий заряд батареи!", level=self.WARN)
                return False
                
        return True
    
    def on_subscribers_timeout(self, expired: List[str]):
        """Обработка таймаутов подписчиков"""
        if "scan" in expired:
            self.handle_error(
                "Потеряна связь с лидаром!",
                level=self.ERROR,
                event_code=self.events.SENSOR_FAILURE
            )
        
        if "status" in expired:
            self.handle_error(
                "Потеряна связь с контроллером робота!",
                level=self.ERROR,
                event_code=self.events.CONTROLLER_FAILURE
            )


def main(args=None):
    rclpy.init(args=args)
    robot_fsm = RobotFSM()
    robot_fsm.run()

if __name__ == '__main__':
    main()
```

## Дополнительные возможности

### Переключение состояний через ROS-сообщения

FSM автоматически подписывается на топик `/set_state` и реагирует на команды смены состояния. Это позволяет удаленно управлять FSM:

```python
# Пример отправки команды из другого узла
state_cmd = StateCommand()
state_cmd.node_name = "robot_fsm" 
state_cmd.state = "move_forward" # или prev для возврата в предыдущее состояние
set_state_pub.publish(state_cmd)
```

### Получение времени нахождения в состоянии

Метод `get_current_state_duration()` возвращает "чистое" время нахождения в текущем состоянии, за исключением периодов блокировки:

```python
def do_work(self):
    duration = self.node.get_current_state_duration()
    
    # Проверка таймаута
    if duration > 10.0:
        self.log(f"Превышено время выполнения: {duration} сек")
        self.node.set_state("idle")
```

### Проверка нахождения в состоянии failure

Метод `is_current_state_failure()` проверяет, находится ли FSM в встроенном состоянии `failure`:

```python
if self.is_current_state_failure():
    self.log("Система в состоянии отказа")
    # Специальная обработка...
```

### Публикация статуса FSM

FSM автоматически публикует свой статус в топик `/{node_name}_status`:

```python
# Пример подписки на статус FSM из другого узла
def fsm_status_callback(msg):
    print(f"Текущее состояние FSM: {msg.current_state}")
    print(f"ID текущего действия: {msg.cur_action_id}")
    print(f"ID последнего действия: {msg.last_action_id}")
    
fsm_status_sub = node.create_subscription(
    StateMachineStatus,
    "/robot_fsm_status",
    fsm_status_callback,
    QoSProfile(depth=10)
)
```
ID текущего и последнего действия могут быть изменены как self.cur_action_id и self.last_action_id в классе FSM (или self.node.(...) в классе состояния)

