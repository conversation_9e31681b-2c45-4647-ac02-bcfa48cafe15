from typing import Dict, Optional, List, Any
from types import SimpleNamespace

from .base_node import BaseNode

from drill_msgs.msg import (
    BoolStamped,
    Permission as PermissionMsg,
    StateMachineStatus,
    StateCommand,
)
from rclpy.qos import QoSProfile, DurabilityPolicy, ReliabilityPolicy


# -----------------------------------------------------------------------------
# Base state classes
# -----------------------------------------------------------------------------

class BaseState:
    """Represents a single state of the FSM."""

    # Log level constants for convenience in states
    DEBUG = BaseNode.DEBUG
    INFO = BaseNode.INFO
    WARN = BaseNode.WARN
    ERROR = BaseNode.ERROR
    FATAL = BaseNode.FA<PERSON>L

    def __init__(
            self,
            *,
            name: str,
            node: "BaseFSM",
            remember_as_prev: bool = True,
            ignore_permission: bool = False,
            ignore_robomode: bool = False,
            ignore_outdated_subs: bool = False,
            ignore_safety_check: bool = False,
            suppress_failure: bool = False,
    ):
        self.name = name
        self.node = node
        # If False, this state is *not* stored as the previous good state
        self.remember_as_prev = remember_as_prev

        # Optional execution flags -----------------------------------------
        self.ignore_permission = ignore_permission
        self.ignore_robomode = ignore_robomode
        self.ignore_outdated_subs = ignore_outdated_subs
        self.ignore_safety_check = ignore_safety_check
        self.suppress_failure = suppress_failure

    # ------------------------------------------------------------------
    # Convenience accessors --------------------------------------------
    # ------------------------------------------------------------------

    @property
    def transition_kwargs(self) -> Dict[str, Any]:
        """Arguments that were supplied when this state was entered."""
        return self.node.transition_kwargs

    @property
    def subs(self):
        """Shorthand for `self.node.subs`."""
        return self.node.subs

    @property
    def node_params(self):
        """All ROS-parameters declared for this node itself."""
        return self.node.node_params

    @property
    def vehicle_params(self):
        """Vehicle‑specific parameter set."""
        return self.node.vehicle_params

    @property
    def global_params(self):
        """Global cross‑node parameter set."""
        return self.node.global_params

    def log(
        self,
        message: str,
        level: int = BaseNode.INFO,
        *,
        event_code: Optional[str] = None,
        require_remote: bool = False,
        period: float = 0.0,
    ) -> None:
        """Shorthand → self.node.log()"""
        return self.node.log(
            message,
            level=level,
            event_code=event_code,
            require_remote=require_remote,
            period=period,
        )

    def handle_error(
            self,
            message: str,
            level: str,
            *,
            event_code: Optional[int] = None,
            require_remote: bool = False,
            period: float = 0.0,
    ) -> None:
        """Shorthand → self.node.handle_error()"""
        return self.node.handle_error(
            message,
            level=level,
            event_code=event_code,
            require_remote=require_remote,
            period=period,
        )

    # ------------------------------------------------------------------
    # Lifecycle hooks – override in subclasses
    # ------------------------------------------------------------------

    def on_transition_to(self):
        """Called immediately after the FSM enters this state."""

    def on_transition_from(self):
        """Called just before the FSM exits this state."""

    def do_work(self):
        """Periodic work executed while the FSM remains in this state."""


class FailureState(BaseState):
    """Built‑in *failure* state – never remembered as previous.

    When entered, it calls stop_control to
    bring the system to a safe halt. No work is performed afterward; the FSM is
    effectively stalled until an external command selects another state.
    """

    def __init__(self, node: "BaseFSM") -> None:
        super().__init__(
            name="failure",
            node=node,
            remember_as_prev=False,
            ignore_permission=True,
            ignore_robomode=True,
            ignore_outdated_subs=True,
            ignore_safety_check=True,
        )

    def on_transition_to(self) -> None:
        self.node.stop_control()

    def do_work(self) -> None:
        self.node.stop_control()


# -----------------------------------------------------------------------------
# Base FSM class
# -----------------------------------------------------------------------------

class BaseFSM(BaseNode):
    """Finite‑state machine node base‑class"""

    # ------------------------------------------------------------------
    # Construction helpers
    # ------------------------------------------------------------------

    def __init__(self, node_name: str):
        super().__init__(node_name)

        # FSM bookkeeping ---------------------------------------------------
        self.states: Dict[str, BaseState] = {}
        self.current_state: Optional[BaseState] = None
        self._prev_state: Optional[BaseState] = None
        self._prev_kwargs: Dict[str, Any] = {}
        self.transition_kwargs: Dict[str, Any] = {}

        # Public action identifiers (expected to be modified by child class)
        self.cur_action_id: int = -1
        self.last_action_id: int = -1

        # Subscriber bookkeeping -------------------------------------------
        self.subs = SimpleNamespace()
        self._subscribers: Dict[str, Dict] = {}
        self.robomode = True
        self.permission = True

        # Timing helpers ----------------------------------------------------
        self._state_enter_time = self.get_time()
        self._prevented_time_acc = 0.0
        self._prevented_start_time: Optional[float] = None

        # QoS presets -------------------------------------------------------
        self.qos_transient = QoSProfile(
            depth=10,
            durability=DurabilityPolicy.TRANSIENT_LOCAL,
            reliability=ReliabilityPolicy.RELIABLE,
        )
        self.qos_default = QoSProfile(depth=10)

        # Built‑in subscriptions -------------------------------------------
        self.robomode_sub = self.create_subscription(
            BoolStamped,
            "/robomode",
            self._robomode_cb,
            qos_profile=self.qos_transient,
        )
        self.perm_sub = self.create_subscription(
            PermissionMsg,
            "/permission",
            self._perm_cb,
            qos_profile=self.qos_transient,
        )
        # Command subscription – external state change requests ------------
        self.set_state_sub = self.create_subscription(
            StateCommand,
            "/set_state",
            self._set_state_cmd_cb,
            qos_profile=self.qos_default,
        )

        # Status publisher --------------------------------------------------
        self.status_pub = self.create_publisher(
            StateMachineStatus,
            f"/{node_name}_status",
            qos_profile=self.qos_default,
        )

        # Register the reserved *failure* state --------------------------
        self._failure_state = FailureState(self)
        self.add_states(self._failure_state)

    # ------------------------------------------------------------------
    # Mandatory methods for subclass
    # ------------------------------------------------------------------

    def stop_control(self) -> None:
        """**Must be overridden.** Stop all motion/actuators safely.

        This is invoked automatically whenever the FSM is unable to operate
        and when it transitions into the *failure*
        state. Implementations should bring the hardware into a safe, inert
        state (e.g. set zero velocities, disable power stages, etc.).
        """
        raise NotImplementedError("stop_control() must be implemented in subclass")

    def safety_check(self) -> bool:
        """**Must be overridden.**

        Return *True* when it is safe to run one control iteration.
        """
        raise NotImplementedError("safety_check() must be implemented in subclass")

    # ------------------------------------------------------------------
    # Public API for child classes
    # ------------------------------------------------------------------

    def add_states(self, *states: "BaseState"):
        for state in states:
            self.states[state.name] = state

    # ..................................................................

    def set_state(self, name: str, **transition_kwargs):
        """Switch FSM to a specified state or to the special ``prev`` state.

        * The reserved state name ``failure`` is always available.
        * The reserved keyword ``prev`` switches back to the last remembered
          *good* state (i.e. one created with ``remember_as_prev=True``).
        """

        # Handle special keyword "prev" --------------------------------
        if name == "prev":
            if self._prev_state is None:
                self.log("No previous state recorded – ignoring 'prev' command", level=self.WARN)
                return
            name = self._prev_state.name
            transition_kwargs = self._prev_kwargs.copy()

        if self.current_state is not None:
            # Early‑out if already in requested state ----------------------
            if self.current_state.name == name:
                self.transition_kwargs = dict(transition_kwargs)
                return

            # Remember current state (and its kwargs) before leaving -------
            if self.current_state.remember_as_prev:
                self._prev_state = self.current_state
                self._prev_kwargs = self.transition_kwargs.copy()

            self.current_state.on_transition_from()

        # Enter new state ------------------------------------------------
        new_state = self.states.get(name)
        if new_state is None:
            raise KeyError(f"Unknown state '{name}' in '{self.node_name}'")
        self.current_state = new_state
        self.transition_kwargs = dict(transition_kwargs)

        # Reset timing helpers -------------------------------------------
        self._state_enter_time = self.get_time()
        self._prevented_time_acc = 0.0
        self._prevented_start_time = None

        new_state.on_transition_to()
        self._publish_status()  # Immediate status on transition


    def add_subscriber(
        self,
        topic: str,
        msg_type,
        name: str,
        timeout: float,
        qos_profile: Optional[QoSProfile] = None,
    ):
        if qos_profile is None:
            qos_profile = self.qos_default

        def _cb(msg, key=name):
            rec = self._subscribers[key]
            rec["msg"], rec["time"] = msg, self.get_time()

        sub = self.create_subscription(msg_type, topic, _cb, qos_profile=qos_profile)
        self._subscribers[name] = {
            "subscriber": sub,
            "msg": None,
            "time": 0.0,
            "timeout": timeout,
        }
        setattr(self.subs, name, None)

    def handle_error(self, message: str, level, event_code=None, require_remote: bool = False, period: float = 0.0):
        self.log(message, level, event_code=event_code, require_remote=require_remote, period=period, _stack_offset=2)
        if level > self.WARN and not self.is_current_state_failure() and not (self.current_state and self.current_state.suppress_failure):
                self.set_state("failure")

    # ------------------------------------------------------------------
    # Hooks to be optionally overridden by child class
    # ------------------------------------------------------------------

    def on_subscribers_timeout(self, expired: List[str]):
        """Called when any custom subscribers time out."""

    def do_work_finally(self) -> None:
        """Called once at the end of every `do_work()` iteration.
        """
        pass

    def do_work_prevented(self) -> None:
        """Called when regular work cannot proceed – stop hardware safely."""
        pass

    # ------------------------------------------------------------------
    # Internal callbacks / helpers
    # ------------------------------------------------------------------

    def _robomode_cb(self, msg: BoolStamped):
        self.robomode = msg.value

    def _perm_cb(self, msg: PermissionMsg):
        self.permission = msg.permission

    def _set_state_cmd_cb(self, msg: StateCommand):
        """Handle external /set_state command."""
        if msg.node_name and msg.node_name != self.node_name:
            return
        self.set_state(msg.state)

    def _do_work_prevented(self):
        """Called when regular work cannot proceed – stop hardware safely."""
        self.stop_control()
        self.do_work_prevented()

    # ------------------------------------------------------------------
    # Status publication helper
    # ------------------------------------------------------------------

    def _publish_status(self):
        """Publish current FSM status via /{node_name}_status."""
        msg = StateMachineStatus()
        msg.header.stamp = self.get_clock().now().to_msg()
        msg.header.frame_id = ""
        msg.cur_action_id = self.cur_action_id
        msg.last_action_id = self.last_action_id
        msg.current_state = self.current_state.name if self.current_state else ""
        self.status_pub.publish(msg)

    # ------------------------------------------------------------------
    # Main loop – called periodically by executor
    # ------------------------------------------------------------------

    def do_work(self):
        """Main loop: runs state's work or prevented hook."""
        now = self.get_time()
        expired_subs: List[str] = []
        waiting_subs: List[str] = []

        # Update subscriber cache & timeouts ------------------------------
        for key, info in self._subscribers.items():
            if info["msg"] is None:
                waiting_subs.append(key)
            elif now - info["time"] > info["timeout"]:
                expired_subs.append(key)
            setattr(self.subs, key, info["msg"])

        if len(waiting_subs):
            self.log(f"Waiting for subs: {waiting_subs}", level=self.WARN, period=5)

        # Ready to do regular work? --------------------------------------
        if self.current_state is None:
            ready = False
        else:
            state = self.current_state
            perm_ok = self.permission or state.ignore_permission
            robomode_ok = self.robomode or state.ignore_robomode
            subs_ok = (not expired_subs and not waiting_subs) or state.ignore_outdated_subs
            ready = perm_ok and robomode_ok and subs_ok

            if ready and not state.ignore_safety_check:
                ready = bool(self.safety_check())

        if ready:
            # Accumulate prevented duration when resuming work ---------------
            if self._prevented_start_time is not None:
                self._prevented_time_acc += now - self._prevented_start_time
                self._prevented_start_time = None
            # Execute the state's periodic work ------------------------------
            self.current_state.do_work()
        else:
            # Log prevented interval start
            if self._prevented_start_time is None:
                self._prevented_start_time = now

            self._do_work_prevented()
            if self.current_state and expired_subs and not self.current_state.ignore_outdated_subs:
                self.on_subscribers_timeout(expired_subs)

        self.do_work_finally()
        self._publish_status()


    # ------------------------------------------------------------------
    # Public helper
    # ------------------------------------------------------------------

    def get_current_state_duration(self) -> float:
        """Time spent in current state, excluding prevented intervals."""
        if self.current_state is None:
            return 0.0

        total = self.get_time() - self._state_enter_time
        prevented = self._prevented_time_acc
        if self._prevented_start_time is not None:
            prevented += self.get_time() - self._prevented_start_time
        return total - prevented

    def is_current_state_failure(self) -> bool:
        """True if FSM is already in the built‑in *failure* state."""
        return self.current_state is not None and self.current_state.name == "failure"
