class Events:
    SW_ERROR = "SW_ERROR"
    TEST_EVENT = "TEST_EVENT"
    RTK_CONN_FAILED = "RTK_CONN_FAILED"
    ACTION_NOT_ACCEPTED = "ACTION_NOT_ACCEPTED"
    ACTION_COMPLETED = "ACTION_COMPLETED"
    WAITING_JACKS_ARM ="WAITING_JACKS_ARM"
    RC_MOVE ="RC_MOVE"
    APPROACH_TIMEOUT = "APPROACH_TIMEOUT"
    APPROACH_DISTANCE_ERROR = "APPROACH_DISTANCE_ERROR"
    SENSOR_FAILURE = "SENSOR_FAILURE"

    # Arm Controller specific events
    ARM_SWITCH_INCONSISTENT = "ARM_SWITCH_INCONSISTENT"
    ARM_STUCK_OPENING = "ARM_STUCK_OPENING"
    ARM_STUCK_CLOSING = "ARM_STUCK_CLOSING"
